import { keys } from 'lodash'

export const rootPath = () =>
  process.env.ROOT_URL || process.env.WEBAPP_SEO_DOMAIN

// Legacy routes
export const legacyUrl = (path = '') => `${process.env.LEGACY_APP_ROOT}/${path}`

// Base full URLs
// We differentiate both URLs below in order to comply on production
// with our SEO friendly URLS. These are meant only for production.
export const adminDomain = process.env.WEBAPP_DOMAIN || ''
export const participationDomain = process.env.WEBAPP_SEO_DOMAIN || ''

export const privacyPolicyUrl = () =>
  process.env.PRIVACY_POLICY_URL ||
  'https://lets.events/pt/politica-de-privacidade/'
export const termsOfUseUrl = () =>
  process.env.TERMS_OF_USE_URL ||
  'https://lets.events/l/politica-de-privacidade-termos-de-uso-e-servicos/'

// Admin routes
export const adminPath = (eventId) =>
  `${adminDomain}/admin${eventId ? `/${eventId}` : ''}`
export const eventAdminPath = (eventId) => `${reportSalesPath(eventId)}` // adminPath(eventId)
export const participantsPath = () => `${adminPath()}/participants`
export const bankAccountsPath = () => `${adminPath()}/wallet`
export const teamAdminPath = () => `${adminPath()}/team`
export const promoCodesPath = (eventId) => `${adminPath(eventId)}/promo-codes`
export const trackingLinksPath = (eventId) =>
  `${adminPath(eventId)}/tracking-links`
export const editPromoCodePath = (eventId, couponId) =>
  `${promoCodesPath(eventId)}/${couponId}`
export const createPromoCodePath = (eventId) => `${promoCodesPath(eventId)}/new`
export const createEventPath = () => legacyUrl(`events/new`) // `${adminPath()}/new`
export const editEventPath = (eventId, context) => {
  let path = `${adminPath(eventId)}/edit`
  if (context) {
    path = `${path}/${context}`
  }
  return path
}
export const duplicateEventPath = (eventId) => `${adminPath(eventId)}/duplicate`
export const myEventsPath = () => `${adminPath()}/events`
export const myTeamPath = () => `${adminPath()}/team`
export const myWalletPath = (viewerId) => `${adminPath()}/${viewerId}/wallet`
export const organizerPagePath = () => `${adminPath()}/organizer`
export const adminConfigPath = () => `${adminPath()}/configs`
export const reportSalesPath = (eventId) => `${adminPath(eventId)}/report/sales`
export const eventDetailsPath = (eventId) => `${adminPath(eventId)}/page`
export const eventTeamPath = (eventId) => `${adminPath(eventId)}/team`
export const eventConfigurationsPath = (eventId) =>
  `${adminPath(eventId)}/configurations`
export const eventOrdersPath = (eventId) => `${adminPath(eventId)}/orders`
export const eventCheckinPath = (eventId) =>
  legacyUrl(`events/${eventId}#admin/checkin`) // `${adminPath(eventId)}/checkin`

export const eventPDVPath = (eventId) => `${adminPath(eventId)}/pdv`

export const eventParticipantsCheckinFile = (eventId) =>
  legacyUrl(`events/${eventId}/generate_lists_report`) // `${adminPath(eventId)}/checkin`
export const eventTicketsPath = (eventId) => `${adminPath(eventId)}/tickets`
export const eventPromoCodesPath = (eventId) =>
  `${adminPath(eventId)}/promo-codes`
export const eventTrackingLinksPath = (eventId) =>
  `${adminPath(eventId)}/tracking-links`
export const eventTicketReport = (eventId, ticketId) =>
  `${legacyUrl(
    `events/${eventId}/generate_lists_report?channel_id=${ticketId}`
  )}`
export const eventListsPath = (eventId) => `${adminPath(eventId)}/lists`
export const affiliationsPath = () => `${adminDomain}/affiliate`
export const myAffiliationsPath = () => `${affiliationsPath()}/affiliations`
export const formsPath = (eventId, isTicket = false) =>
  `${adminPath(eventId)}/forms/${isTicket ? 'tickets' : 'lists'}`

// Participant routes
export const acceptInvitePath = (token) => `${participationDomain}/t/${token}`
export const eventsPath = () => `${participationDomain}`
export const eventPath = (eventId) => `${participationDomain}/e/${eventId}`
export const eventParticipationsPath = (eventId, channelId) =>
  `${participationDomain}/e/${eventId}/participation/${channelId}`

export const eventParticipatePath = (eventId, type, channelId, isConfirming) =>
  `${eventPath(eventId)}/${type}${channelId ? `/${channelId}` : ''}${
    isConfirming ? '/confirm' : ''
  }`

export const letsPlayPath = (eventSlug, participationId) =>
  `${participationDomain}/play/${eventSlug}/${participationId}`

export const supportPath = () =>
  process.env.SUPPORT_PATH || 'https://support.lets.events'
export const purchasePath = (
  eventId,
  purchaseId,
  isDonationOnly = false,
  queryParams
) => {
  let path = `${eventPath(eventId)}/purchase/${purchaseId}`

  if (isDonationOnly) path += `/email`

  if (queryParams) {
    const params = new URLSearchParams(queryParams).toString()
    path += `?${params}`
  }

  return path
}
export const legacyManualAddOrder = (eventId) =>
  legacyUrl(`events/${eventId}/#admin/manual-add-tickets`)
export const faqEmailNotReceivedPath = `${supportPath()}/hc/pt-br/articles/360018755971`

export const profileEditPath = () => `${participationDomain}/user/profile/edit`

// Tickets (participant) routes
const myPath = () => `${adminDomain}/my`
export const myTicketsPath = () => `${myPath()}/tickets`
export const purchaseDetailPath = (purchaseId) =>
  `${myTicketsPath()}/${purchaseId}`
export const contactOrganizerPath = (purchaseId) =>
  `${purchaseDetailPath(purchaseId)}/contact-organizer`
export const ticketTransferPath = (purchaseItemId) =>
  `${myTicketsPath()}/transfer/${purchaseItemId}`

// Authentication routes
const authenticationPath = (path) =>
  `${rootPath()}/authentication${path ? `/${path}` : ''}`
export const loginPath = () => authenticationPath()
export const resetPasswordPath = () => `${authenticationPath()}/reset-password`

// Lets.Connect authentication
export const letsConnectAuthenticationPath = (participationId) =>
  `${process.env.LETS_API}/lets_connect/auth/${participationId}`

export const letsConnectProducersAuthenticationPath = (eventId) =>
  `${process.env.LETS_API}/lets_connect/auth/producers/${eventId}`

export const pushRoute = ({ path, query }) => {
  let href = path

  if (query) {
    keys(query).forEach((key, index) => {
      if (index === 0) {
        href += '?'
      } else {
        href += '&'
      }
      href = `${href + key}=${query[key]}`
    })
  }

  window.location.href = href
}

export const eventParticipantsCsvPath = (
  eventId,
  page = 1,
  updatedAfter = ''
) => {
  const path = `${process.env.LETS_API}/api/events/${eventId}/participants`
  const query = `page=${page}&page_size=20000&meta_information=true&updated_after=${updatedAfter}`
  return `${path}?${query}`
}
