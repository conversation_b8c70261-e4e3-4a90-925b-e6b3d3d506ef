.new-form-layout {
  --input-height: 38px;
  --input-boder-radius: 8px;

  &__title {
    font-family: Work Sans;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    margin: 0;
    color: #34363c;
  }

  &__label {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    margin: 0;
    color: #34363c;
  }

  &__group-fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 20px;
  }

  &__divider {
    border-bottom: 1px solid #c2c3c7;
  }

  &__new-error-field {
    & .field-error-msg__wrapper {
      display: flex;
    }
  }

  & .bp4-icon-calendar {
    display: none;
  }

  & .address-field__title {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0em;
    text-align: left;
    color: #34363c;
    margin-bottom: 10px !important;
  }

  & .autocomplete-dropdown.has-content {
    padding: 16px 0;
    border: 1px solid #eaebf0;
    border-radius: 5px;
    box-shadow: -2px 0px 4px 0px #0000001a, 0px 4px 4px 0px #00000033;
    background-color: white;
    margin-top: 4px;
    width: 100%;

    & .autocomplete-dropdown__item {
      background-color: white;
      font-family: Work Sans;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.42px;
      text-align: left;
      margin: 0;
      padding: 8px 16px;

      display: grid;
      grid-template-columns: auto auto;
      column-gap: 8px;
      justify-content: start;
      align-items: center;

      &:hover {
        background-color: #b9c8e3;

        & svg {
          color: white;
        }
      }
    }

    & .autocomplete-dropdown__item--active {
      background-color: #b9c8e3;
    }
  }

  & .bp4-form-group {
    margin: 0;

    & .bp4-form-helper-text {
      margin-top: 10px !important;
      font-size: 13px !important;
      color: #808289 !important;
    }

    &.phone {
      & input {
        padding-left: 50px !important;
      }
    }
  }

  & .address-field__inputs-container {
    column-gap: 30px;
    grid-template-columns: 1fr 1fr;
  }

  & .bp4-label {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: 0em;
    text-align: left;
    color: #34363c;
    margin-bottom: 14px !important;

    & .bp4-text-muted {
      color: #808289;
      font-weight: 400;
    }
  }

  & .bp4-checkbox {
    margin-bottom: 14px !important;
  }

  & .checkbox {
    gap: 10px;
  }

  & .checkbox__box {
    width: 18px;
    height: 18px;
  }

  & .checkbox__label {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 16.42px;
    text-align: left;
    margin: 0;
    color: #4c4f54;
  }

  & .bp4-select {
    &::after {
      top: 50%;
      transform: translateY(-50%);
      content: '\f14A';
    }

    border: 1px solid #c2c3c7 !important;
    height: var(--input-height) !important;
    border-radius: var(--input-boder-radius) !important;
    width: 100%;

    & > select {
      box-shadow: none;
      background-color: white;
      height: 100%;
      border-radius: var(--input-boder-radius);
      max-width: unset;
      padding: 0 14px;

      font-family: Work Sans;
      font-size: 13px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0em;
      text-align: left;
      color: #4c4f54;
    }
  }

  & .bp4-form-content {
    & .bp4-input-group > input,
    .react-tel-input > input,
    .bp4-input {
      border: 1px solid #c2c3c7;
      border-radius: var(--input-boder-radius);
      height: var(--input-height);
      padding: 0 14px;
      box-shadow: none;
      font-family: Work Sans;
      font-size: 13px !important;
      font-weight: 400;
      line-height: 16px !important;
      letter-spacing: 0em;
      text-align: left;
      color: #4c4f54;
    }

    & textarea.bp4-input {
      border: 1px solid #c2c3c7 !important;
      border-radius: var(--input-boder-radius) !important;
      height: 36px;

      border: none;
      padding: 10px 14px;
      box-shadow: none;
      border-radius: var(--input-boder-radius);

      font-family: Work Sans;
      font-size: 13px !important;
      font-weight: 400;
      line-height: 16px !important;
      letter-spacing: 0em;
      text-align: left;
      color: #4c4f54;

      resize: none;
      overflow: hidden;
    }

    & > div.horizontal {
      display: grid;
      column-gap: 16px;
      grid-auto-flow: column;
      grid-auto-columns: auto;
      justify-content: start;

      & .bp4-control {
        margin: 0;
      }
    }
  }

  & .bp4-input-group.bp4-intent-danger .bp4-input {
    border-color: #cf3c4f;
  }

  & .react-tel-input {
    & .flag-dropdown {
      border-top: none;
      border-bottom: none;
      border-left: none;
    }

    & > input {
      padding: 0 14px 0 50px;
    }

    & input:focus {
      box-shadow: none;
    }

    &--error {
      border-radius: var(--input-boder-radius);
      border: 1px solid #db3737;
      overflow: hidden;
    }
  }

  & .bp4-input-action {
    top: 50%;
    right: 16px !important;
    transform: translateY(-50%);
    & .bp4-tag {
      border-radius: 6px !important;
      background-color: rgba(234, 235, 240, 1);
    }
  }

  & .bp4-input-left-container {
    top: 50% !important;
    transform: translateY(-50%) !important;
    padding: 0 12px 0 12px;
  }

  & .bp4-checkbox {
    font-family: Work Sans;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    color: #4c4f54;

    & > .bp4-control-indicator {
      width: 16px;
      height: 16px;
      box-shadow: none;
      border: 1px solid #c2c3c7;
      border-radius: 3px;

      &::before {
        width: 14px;
        height: 14px;
        background-position: center;
      }
    }
  }

  & .bp4-radio {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 16.42px;
    text-align: left;
    color: #4c4f54;
    display: flex;
    align-items: center;
    justify-content: start;
    margin-bottom: 14px;
  }

  .radio-field {
    &__option-wrapper {
      display: flex;
      flex-direction: column;
    }
    &__helper-text {
      &--below {
        font-family: 'Work Sans';
        font-size: 12px;
        line-height: 18px;
        color: #4c4f54;
        margin-top: 6px;
      }
    }
  }
}
