import React from 'react'
import Spinner from 'src/components/Spinner'
import { Intent, Classes } from '@blueprintjs/core'
import { find, isFunction } from 'lodash'
import Toaster from 'src/components/Toaster'
import './scss/promise-notification.scss'

const updateNotification = (key, message, intent, icon, timeout = 8000) => {
  const props = {
    message,
    intent,
    icon,
    timeout,
  }

  Toaster.show(props, key)
}

const LoadingNotification = ({ message }) => (
  <div className="loading-notification-container">
    <Spinner intent={Intent.NONE} className={Classes.SMALL} />
    <span className="loading-notification-msg">{message}</span>
  </div>
)

const onSuccess = (key, successMsg, timeout) => {
  updateNotification(key, successMsg, Intent.SUCCESS, 'tick', timeout)
}

const onFail = (key, failMsg, timeout) => {
  updateNotification(key, failMsg, Intent.DANGER, 'error', timeout)
}

const PromiseNotification = (
  promise,
  loadingMsg,
  successMsg,
  failMsg,
  checkForErrors,
  timeout,
  hideErrors
) => {
  const key = Toaster.show({
    message: <LoadingNotification message={loadingMsg} />,
    intent: Intent.NONE,
    icon: undefined,
  })

  let promiseInstance = promise
  if (isFunction(promise)) {
    promiseInstance = promise()
  }

  if (promiseInstance.then === undefined) {
    throw new Error(
      'PromiseNotification first parameter must be a promise object or a function that returns a promise'
    )
  }

  return promiseInstance
    .then((result) => {
      if (checkForErrors) {
        const error = checkForErrors(result)
        if (error) {
          throw new Error(error)
        }
      }

      const hasErrors = find(result?.data, ['__typename', 'ValidationErrors'])

      if (hasErrors) {
        throw new Error(hasErrors.errors[0])
      }

      onSuccess(
        key,
        isFunction(successMsg) ? successMsg(result) : successMsg,
        timeout
      )
      return result
    })
    .catch((error) => {
      if (!hideErrors)
        onFail(
          key,
          isFunction(failMsg) ? failMsg(error) : error.message || failMsg,
          timeout
        )

      return { error }
    })
}

export default PromiseNotification
