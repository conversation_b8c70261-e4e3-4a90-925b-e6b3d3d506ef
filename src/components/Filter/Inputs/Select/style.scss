.filter-select {
  width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Work Sans';
  position: relative;

  &__label {
    font-size: 14px;
    font-weight: bold;
    color: #4c4f54;
    margin-bottom: 10px;
  }

  &__container {
    width: 100%;
    position: relative;

    &__input {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border: 1px solid #c2c3c7;
      border-radius: 8px;
      cursor: pointer;

      &__placeholder {
        font-size: 14px;
        color: #808289;
        user-select: none;
        margin: 0;
      }

      &__options {
        width: 100%;
        position: absolute;
        top: calc(100% + 5px);
        left: 0;
        max-height: 200px;
        overflow: auto;
        background-color: #fff;
        border-radius: 5px;
        z-index: 1;
        padding: 10px 0;
        box-shadow: 0 2px 7px 0 rgba(0, 0, 0, 0.2);

        &__item {
          padding: 10px;
          font-size: 14px;
          color: #4c4f54;
          cursor: pointer;
          transition: background-color 0.2s;
          display: flex;
          align-items: center;
          gap: 5px;

          &:hover {
            background-color: #f2f2f2;
          }
        }
      }
    }
  }

  &__selected-values {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    row-gap: 5px;
    margin-top: 7px;

    &__item {
      display: flex;
      align-items: center;
      gap: 5px;

      p {
        font-size: 12px;
        color: #4c4f54;
      }

      svg {
        font-size: 9px;
        cursor: pointer;
        border: 1px solid #4c4f54;
        border-radius: 2px;
        padding: 1px 4px;
      }
    }
  }
}
