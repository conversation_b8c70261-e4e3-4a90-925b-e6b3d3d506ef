import React, { Component } from 'react'
import { InputGroup, Tag, Classes } from '@blueprintjs/core'
import PropTypes from 'prop-types'
import { omit } from 'lodash'
import { maskText } from '../../utils'
import './scss/_main.scss'

const applyGridClassName = (grid, className) => {
  let finalClassName = className != null ? className : ''
  if (grid) {
    finalClassName = `form-grid__input form-grid__input--${grid}`
  }
  return finalClassName
}

class Input extends Component {
  static propTypes = {
    grid: PropTypes.string,
    maxChars: PropTypes.number,
    autoFill: PropTypes.object,
    whitelist: PropTypes.instanceOf(RegExp),
    mask: PropTypes.object,
    round: PropTypes.bool,
    large: PropTypes.bool,
  }

  constructor(props) {
    super(props)
    const { autoFocus } = props
    this.state = { readOnlyToPreventMobileKeyboardOnAutoFocus: autoFocus }
  }

  render() {
    const {
      autoFill,
      children,
      className,
      grid,
      large,
      mask,
      maxChars,
      onChange,
      onBlur,
      readOnly,
      round,
      value,
      whitelist,
      inputRef,
      autoComplete,
      showCharCount = true,
    } = this.props

    const { readOnlyToPreventMobileKeyboardOnAutoFocus } = this.state

    /* Trick to keep autoFocus working but preventing the mobile keyboard from appearing
     * The idea is to put the focus on the input element while it is on a `readOnly`
     * flag, which will cause the mobile keyboard not to open. We then remove the
     * readOnly flag, allowing user input */
    if (readOnlyToPreventMobileKeyboardOnAutoFocus) {
      setTimeout(() => {
        this.setState({ readOnlyToPreventMobileKeyboardOnAutoFocus: false })
      }, 10)
    }

    const rightElement = () => {
      if (!maxChars) return null
      const rawValue = value && whitelist ? value.replace(whitelist, '') : value
      return (
        <Tag className={Classes.MINIMAL}>
          {maxChars - (rawValue ? rawValue.length : 0)}
        </Tag>
      )
    }

    const handleOnChange = (e, shouldAutoComplete) => {
      let value = e.target.value
      if (whitelist) {
        value = value.replace(whitelist, '')
      }
      if (maxChars) {
        value = value.slice(0, maxChars)
      }
      if (shouldAutoComplete && value.length > 0) {
        const missingCharsCount = maxChars - value.length
        const side = autoFill.side
        for (let i = 0; i < missingCharsCount; i++) {
          if (side === 'left') {
            value = `${autoFill.char}${value}`
          } else {
            value = `${value}${autoFill.char}`
          }
        }
      }
      if (mask) value = maskText(value, mask)
      if (onChange) onChange(value)
    }

    const handleOnBlur = (e) => {
      if (onBlur) return onBlur(e)
      if (maxChars > 0 && autoFill) handleOnChange(e, true)
    }

    const props = omit(this.props, ['maxChars', 'showCharCount'])

    return (
      <InputGroup
        rightElement={showCharCount && rightElement()}
        {...props}
        round={null}
        large={null}
        className={applyGridClassName(
          grid,
          `${className} ${round && Classes.ROUND} ${large && Classes.LARGE}`
        )}
        onChange={handleOnChange}
        onBlur={handleOnBlur}
        readOnly={readOnly || readOnlyToPreventMobileKeyboardOnAutoFocus}
        inputRef={inputRef}
        autoComplete={autoComplete}
      >
        {children}
      </InputGroup>
    )
  }
}

export { Input, applyGridClassName }
