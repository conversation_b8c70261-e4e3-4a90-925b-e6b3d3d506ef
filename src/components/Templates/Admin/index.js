import { useRouter } from 'next/router'
import Redirect from 'src/components/Redirect'
import NewSidebar from 'src/components/NewSidebar'
import { withTranslations } from 'lets-i18n'
import {
  faCalendarDays,
  faLaptop,
  faUsers,
  faFileInvoiceDollar,
  faUserGroup,
  faCog,
} from '@fortawesome/free-solid-svg-icons'
import ScreenDetector from 'src/components/ScreenDetector'
import AdminNavbar from './AdminNavbar'
import { useState } from 'react'
import './style.scss'

import {
  myEventsPath,
  bankAccountsPath,
  participantsPath,
  organizerPagePath,
  teamAdminPath,
  adminConfigPath,
} from 'src/utils/routes'

const AdminTemplate = ({ t, children, isMobile, showHeader }) => {
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile)

  const sidebarItems = [
    {
      href: myEventsPath(),
      title: t('admin.myEvents'),
      iconLeft: faCalendarDays,
    },
    {
      href: bankAccountsPath(),
      title: t('admin.wallet'),
      iconLeft: faFileInvoiceDollar,
    },
    {
      href: participantsPath(),
      title: t('admin.participants'),
      iconLeft: faUsers,
    },
    {
      href: teamAdminPath(),
      title: t('admin.team'),
      iconLeft: faUserGroup,
    },
    {
      href: organizerPagePath(),
      title: t('admin.organizationPage'),
      iconLeft: faLaptop,
    },
    {
      href: adminConfigPath(),
      title: t('admin.adminConfig'),
      iconLeft: faCog,
    },
  ]

  return (
    <Redirect.IfNotLogged>
      {!showHeader && <AdminNavbar setSidebarOpen={setSidebarOpen} />}
      <div className="admin-template-container">
        {!showHeader && (
          <NewSidebar
            items={sidebarItems}
            header={<></>}
            open={isMobile ? sidebarOpen : false}
            setOpen={setSidebarOpen}
            router={router}
          />
        )}
        <div className="admin-template-container__content">{children}</div>
      </div>
    </Redirect.IfNotLogged>
  )
}

export default ScreenDetector(withTranslations(AdminTemplate))
