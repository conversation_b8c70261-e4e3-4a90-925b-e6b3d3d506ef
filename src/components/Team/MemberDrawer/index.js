import { withTranslations } from 'lets-i18n';
import Drawer from 'src/components/NewDrawer';
import InputField from 'src/components/FormUtils/InputField';
import Spacing from 'src/components/Spacing';
import Checkbox from 'src/components/Checkbox';
import BaseButton from 'src/components/BaseButton';
import MemberConfirmModal from 'src/components/Team/MemberConfirmModal';
import { required } from 'src/components/FormUtils/validators'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClose } from '@fortawesome/free-solid-svg-icons';
import { Form } from 'react-final-form';
import { useEffect, useState } from 'react';
import { useMutation } from '@apollo/client';
import { Intent } from '@blueprintjs/core';
import { createMemberMutation, updateMutation } from '../queries';
import Toaster from 'src/components/Toaster'

import './scss/_main.scss';

const MemberDrawer = ({ 
  t, 
  eventId, 
  open, 
  setOpen, 
  onChange, 
  selectedMember, 
  isOrganization = false 
}) => {
  const initialPermissions = [
    {title: t('team.memberDrawer.permissions.MASTER_ADMINS'), value: 'MASTER_ADMINS', checked: false},
    {title: t('team.memberDrawer.permissions.DOORMEN'), value: 'DOORMEN', checked: false},
    {title: t('team.memberDrawer.permissions.SALES_ANALYSTS'), value: 'SALES_ANALYSTS', checked: false},
    {title: t('team.memberDrawer.permissions.PDV_OPERATOR'), value: 'PDV_OPERATOR', checked: false},
  ];

  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [addMemberMutation] = useMutation(createMemberMutation);
  const [updateMemberMutation] = useMutation(updateMutation);
  const [permissions, setPermissions] = useState(initialPermissions);
  const [memberToAdd, setMemberToAdd] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const onChangePermission = (checked, index) => {
    setPermissions(prev => {
      let updatedPermissions = prev.map((permission, i) =>
        i === index ? { ...permission, checked } : permission
      );
  
      if (updatedPermissions[index]?.value === 'MASTER_ADMINS' && checked) {
        updatedPermissions = updatedPermissions?.map(permission => ({
          ...permission,
          checked: true,
        }));
      }

      return updatedPermissions;
    });
  }

  const getSelectedPermissions = () => {
    const selectedPermissions = permissions
      .filter((permission) => permission.checked)
      .map((permission) => permission.value);
    
    if (selectedPermissions.includes('MASTER_ADMINS')) {
      return ['MASTER_ADMINS'];
    }

    return selectedPermissions;
  }

  const handleSubmit = (values, form) => {
    const roles = getSelectedPermissions();

    if (!roles.length) return;

    if (selectedMember) {
      updateMember(selectedMember.id, getSelectedPermissions(), form);
    } else {
      if (isOrganization) {
        setConfirmModalOpen(true);
        setMemberToAdd({ eventId, name: values.name, email: values.email, roles, form});
      } else {
        addMember(eventId, values.name, values.email, roles, form);
      }
    }
  }

  const updateMember = async (id, roles, form) => {
    setIsLoading(true);
    await updateMemberMutation({
      variables: {
        input: {
          id: id,
          roles: roles
        },
      },
      onCompleted: () => {
        handleClose(form);
        Toaster.show({
          message: t('team.memberDrawer.editSuccessMemberMessage'),
          intent: Intent.SUCCESS,
        });
        onChange();
      },
    });
    setIsLoading(false);
  }

  const addMember = async (eventId, name, email, roles, form) => {
    setIsLoading(true);
    await addMemberMutation({
      variables: {
        input: {
          event_id: eventId,
          email: email,
          roles: roles
        },
      },
      onCompleted: (data) => {
        setMemberToAdd(null);
        setConfirmModalOpen(false);
        handleClose(form);
        onChange();
        setIsLoading(false);

        if (data?.create_team_membership?.errors) {
          Toaster.show({
            message: data?.create_team_membership?.errors[0],
            intent: Intent.DANGER,
          });
        } else {
          Toaster.show({
            message: t('team.memberDrawer.addSuccessMemberMessage'),
            intent: Intent.SUCCESS,
          });
        }
      },
      onError: () => {
        setIsLoading(false);
      }
    });
  }

  const handleClose = (form) => {
    form?.reset?.();
    setOpen(false); 
    setPermissions(initialPermissions);
  }

  useEffect(() => {
    if (selectedMember) {
      selectedMember?.roles?.forEach((role) => {
        const permissionIndex = permissions.findIndex((permission) => permission.value === role);
        const permission = permissions[permissionIndex];
  
        onChangePermission(selectedMember?.roles?.includes(permission.value), permissionIndex);
      });
    } else {
      permissions.forEach((permission, index) => {
        onChangePermission(selectedMember?.roles?.includes(permission.value), index);
      });
    }
  }, [selectedMember])

  return (
    <>
      <Drawer open={open} setOpen={setOpen}>
        <Drawer.Header
          title={selectedMember ? t('team.memberDrawer.editTitle') : t('team.memberDrawer.inviteTitle')}
          rightIcon={
            <FontAwesomeIcon icon={faClose} size='xl' onClick={handleClose} />
          }
        />
        <Drawer.Body>
          <Form 
            onSubmit={handleSubmit}
            render={({ handleSubmit, form }) => {
              return (
                <form className="new-form-layout member-drawer-form" onSubmit={handleSubmit}>
                  {selectedMember && (
                    <>
                      <h6 className='member-drawer-form__label'>{t('team.memberDrawer.name')}</h6>
                      <p className='member-drawer-form__value'>{selectedMember?.member_name}</p>
                    </>
                  )}
                  {selectedMember && (<Spacing size={20} />)}
                  {selectedMember ? (
                    <>
                      <h6 className='member-drawer-form__label'>{t('team.memberDrawer.email')}</h6>
                      <p className='member-drawer-form__value'>{selectedMember?.member_email}</p>
                    </>
                  ) : (
                    <InputField 
                      name='email'
                      label={t('team.memberDrawer.email')}
                      validate={required(t('team.memberDrawer.mandatoryField'))}
                    />
                  )}
                  <Spacing size={20} />
                  <div className='permissions'>
                    <h6>{t('team.memberDrawer.permissions.title')}</h6>
                    <p>{t('team.memberDrawer.permissions.description')}</p>
                    {permissions.map((permission, index) => (
                      <>
                        <Spacing size={index === 1 ? 20 : 14} />
                        <Checkbox 
                          label={permission.title}
                          checked={permission.checked}
                          disabled={permission.value != 'MASTER_ADMINS' && getSelectedPermissions().includes('MASTER_ADMINS')}
                          onChange={(e) => onChangePermission(e, index)}
                        />
                      </>
                    ))}
                  </div>
                  <div className='divider' />
                  <div className='member-drawer-form__buttons'>
                    <BaseButton loading={isLoading}>
                      {selectedMember ? t('team.memberDrawer.save') : t('team.memberDrawer.continue')}
                    </BaseButton>
                    <BaseButton 
                      onClick={() => handleClose(form)}
                      variant="outlined"
                      colorTheme="primary-light"
                      disableShadow
                    >
                      {t('team.memberDrawer.cancel')}
                    </BaseButton>
                  </div>
                </form>
              );
            }}
          />
        </Drawer.Body>
      </Drawer>
      <MemberConfirmModal
        open={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        onConfirm={() => addMember(memberToAdd.eventId, memberToAdd.name, memberToAdd.email, memberToAdd.roles, memberToAdd.form)}
      />
    </>
  );
}

export default withTranslations(MemberDrawer);
