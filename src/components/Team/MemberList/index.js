import MemberRow from "src/components/Team/MemberRow";
import { withTranslations } from "lets-i18n";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faInfoCircle } from "@fortawesome/free-solid-svg-icons";
import { Tooltip } from "react-tooltip";

import './scss/_main.scss';

const MemberList = ({
  t,
  title, 
  members, 
  hasActions, 
  onRemoveMember, 
  onEditMember, 
  onResendEmail,
  search,
  info
}) => {
  const roles = {
    'OWNER': {
      'title': t('team.permissions.OWNER')
    },
    'MASTER_ADMINS': {
      'title': t('team.permissions.MASTER_ADMINS')
    },
    'DOORMEN': {
      'title': t('team.permissions.DOORMEN')
    },
    'SALES_ANALYSTS': {
      'title': t('team.permissions.SALES_ANALYSTS')
    },
    'PDV_OPERATOR': {
      'title': t('team.permissions.PDV_OPERATOR')
    }
  }

  return (
    <>
      {(members.length > 0 && (!search || !title)) && (
        <p className='team__title'>
          {title}
          {info && (
            <>
              <Tooltip 
                id={info}
                place='bottom-end'
                content={info} 
                style={{ maxWidth: 250, textAlign: 'center', zIndex: 1000 }} 
              /> 
              <FontAwesomeIcon icon={faInfoCircle} size="sm" color="#1E2023" data-tooltip-id={info} />
            </>
          )}
        </p>
      )}
      {members.length > 0 && (
        <div>
          {members.map((member) => (
            <MemberRow 
              id={member.id}
              name={member.member_name} 
              email={member.member_email} 
              isOwner={member.is_team_owner}
              roles={member.is_team_owner ? [roles.OWNER] : member.roles.map((role) => roles[role])}
              team={member.team}
              accepted={member.state}
              onDelete={onRemoveMember}
              onEdit={onEditMember}
              hasActions={hasActions}
              onResend={onResendEmail}
            />
          ))}
        </div>
      )}
    </>
  );
}

export default withTranslations(MemberList);
