import React, { useState, useCallback, useMemo } from 'react'
import { Form } from 'react-final-form'
import m from 'moment-timezone'
import Router from 'next/router'
import { useMutation } from '@apollo/client'

import InputField from 'src/components/FormUtils/InputField'
import LocationSearchInput from 'src/components/Input/LocationSearchInput'
import FormGroup from 'src/components/FormGroup'
import Spacing from 'src/components/Spacing'
import RadioFields from 'src/components/FormUtils/RadioFields'
import BaseButton from 'src/components/BaseButton'
import Checkbox from 'src/components/Checkbox'
import InlineDateTimeInput from 'src/components/Input/InlineDateTimeInput'
import ErrorsField from 'src/components/FormUtils/ErrorsField'
import PromiseNotification from 'src/components/PromiseNotification'

import { required } from 'src/components/FormUtils/validators'
import { createEventMutation } from '../../queries'
import { eventDetailsPath, myEventsPath } from 'src/utils/routes'
import { isGoogleCampaign } from 'src/utils/googleCampaign'
import { useFacebookPixelAPI } from 'src/hooks/useFacebookPixelAPI'
import { pushGTMEvent } from 'config/gtm'

import '../main.scss'

const PUBLIC_EVENT = 'public'
const PRIVATE_EVENT = 'private'

const EventCTATypesForTicket = {
  BUY_TICKETS: 'BUY_TICKETS',
  SUBSCRIBE: 'SUBSCRIBE',
  CUSTOM: 'CUSTOM',
}

const EventForm = ({
  t,
  userEvents,
  eventCreated,
  setEventCreated,
  backStep,
  skipOrganizationCreation,
}) => {
  const [venue, setVenue] = useState('')
  const [error, setError] = useState()
  const [isOnline, setIsOnline] = useState(false)
  const [toBeAnnounced, setToBeAnnounced] = useState(false)
  const [eventDates, setEventDates] = useState({
    startsAt: null,
    endsAt: null,
  })
  const [selectedLocation, setSelectedLocation] = useState(null)

  const { trackCustomEvent } = useFacebookPixelAPI()

  const [createEventRequest, { loading }] = useMutation(createEventMutation)

  const today = m().startOf('day').toDate()

  const onDateChange = useCallback((fieldName, newDate) => {
    setEventDates((prev) => ({ ...prev, [fieldName]: newDate }))
  }, [])

  const favLocations = useMemo(() => {
    if (!userEvents) return []

    const maxLocationCount = 3
    const addressList = []

    const haveAddress = (address) => {
      for (let i = 0; i < addressList.length; i++) {
        if (address.one_line === addressList[i].one_line) return true
      }
      return false
    }

    for (let i = userEvents.length - 1; i >= 0; i--) {
      const event = userEvents[i]
      const address = event?.address
      if (!address || !address.one_line) continue

      if (!haveAddress(address)) {
        addressList.push(address)
        if (addressList.length >= maxLocationCount) break
      }
    }

    return addressList
  }, [userEvents])

  const validateForm = () => {
    const errors = {}

    if (!eventDates.startsAt) {
      errors.startsAt = t('global.createEventDialog.form.startsAtRequired')
    }
    if (!eventDates.endsAt) {
      errors.endsAt = t('global.createEventDialog.form.endsAtRequired')
    }
    if (
      eventDates.startsAt &&
      eventDates.endsAt &&
      m(eventDates.startsAt).isAfter(eventDates.endsAt)
    ) {
      errors.dateRange = t(
        'global.createEventDialog.form.startDateIsAfterEndDate'
      )
    }

    if (!isOnline && !toBeAnnounced && !selectedLocation) {
      errors.selectedLocation = t('global.createEventDialog.form.venueRequired')
    }

    return errors
  }

  const handleFormSubmit = async (values) => {
    setError('')

    const formattedStartsAt = eventDates.startsAt
      ? m(eventDates.startsAt).toISOString()
      : null
    const formattedEndsAt = eventDates.endsAt
      ? m(eventDates.endsAt).toISOString()
      : null

    const input = {
      name: values.eventName,
      location_type: toBeAnnounced
        ? 'TO_BE_DEFINED'
        : isOnline
        ? 'ONLINE'
        : 'PHYSICAL',
      online_event_type: 'OTHER',
      online_event_link: values.eventLink,
      address: {
        ...(selectedLocation?.obj ?? {}),
        name: values.adressName,
        complement: values.adressComplement,
      },
      public: values.eventType === PUBLIC_EVENT,
      published: false,
      utc_offset: new Date().getTimezoneOffset() / -60,
      starts_at: formattedStartsAt,
      ends_at: formattedEndsAt,
      ctas: {
        ticket: {
          type: values.buttonPreview,
          label:
            values.buttonPreview === EventCTATypesForTicket.CUSTOM
              ? values.customButtonText
              : null,
        },
      },
    }

    await trackCustomEvent('NewEventCreated')

    if (isGoogleCampaign()) {
      pushGTMEvent('from_google_campaign', {})
    }

    try {
      const result = await PromiseNotification(
        createEventRequest({ variables: { input } }),
        t('global.createEventDialog.notification.loading'),
        t('global.createEventDialog.notification.success'),
        t('global.createEventDialog.notification.error'),
        null,
        null,
        true
      )

      if (result.error) {
        setError(result.error.message)
        return
      }

      const { data: { create_event: { slug } = {} } = {} } = result
      if (!slug) {
        Router.push(myEventsPath())
      } else {
        Router.push(eventDetailsPath(slug))
      }

      setEventCreated(true)
    } catch (err) {}
  }

  return (
    <>
      {!skipOrganizationCreation && (
        <h3 className="dialog-title">{t('global.createEventDialog.title')}</h3>
      )}
      <Form
        initialValues={{
          eventType: PUBLIC_EVENT,
          buttonPreview: EventCTATypesForTicket.BUY_TICKETS,
        }}
        onSubmit={handleFormSubmit}
        validate={validateForm}
        render={({ handleSubmit, values, form }) => {
          const { customButtonText, buttonPreview } = values

          const buttonPreviewText =
            buttonPreview === EventCTATypesForTicket.CUSTOM
              ? customButtonText
              : t(
                  `global.createEventDialog.form.buttonPreviewOptions.${buttonPreview}`
                )

          const onLocationSelect = (obj, address) => {
            setSelectedLocation({ obj, address })
            form.change('adressName', obj.name)
            setVenue(address)
          }

          const onLocationBlur = () => {
            setVenue(selectedLocation?.address || '')
          }

          return (
            <form onSubmit={handleSubmit} className="new-form-layout">
              <div className="create-event-modal__content">
                <Spacing size={20} />
                <div className={'create-event-modal__box'}>
                  <InputField
                    name="eventName"
                    label={t('global.createEventDialog.form.title')}
                    placeholder={t(
                      'global.createEventDialog.form.titlePlaceholder'
                    )}
                    maxChars={110}
                    validate={required(
                      t('global.createEventDialog.form.nameRequired')
                    )}
                  />
                </div>

                <Spacing size={20} />
                <div className={'create-event-modal__box'}>
                  <FormGroup label={t('global.event.locationType.title')}>
                    <div className="create-event-modal__btn-option-wrapper">
                      <BaseButton
                        dense
                        type="button"
                        colorTheme={isOnline ? 'dark' : null}
                        variant={isOnline ? 'outlined' : null}
                        onClick={() => setIsOnline(false)}
                      >
                        Presencial
                      </BaseButton>
                      <BaseButton
                        dense
                        type="button"
                        colorTheme={!isOnline ? 'dark' : null}
                        variant={!isOnline ? 'outlined' : null}
                        onClick={() => setIsOnline(true)}
                      >
                        On-line
                      </BaseButton>
                    </div>
                  </FormGroup>

                  {!toBeAnnounced && <Spacing size={20} />}

                  {toBeAnnounced ? (
                    <>
                      <Spacing size={20} />
                      <p className="new-form-layout__label">
                        {isOnline
                          ? ''
                          : t('global.createEventDialog.form.venueLabel')}
                      </p>
                    </>
                  ) : isOnline ? (
                    <InputField
                      name="eventLink"
                      label={t('global.createEventDialog.form.linkLabel')}
                      helperText={t(
                        'global.createEventDialog.form.linkHelperText'
                      )}
                      placeholder={t(
                        'global.createEventDialog.form.linkPlaceholder'
                      )}
                      validate={required(
                        t('global.createEventDialog.form.linkRequired')
                      )}
                    />
                  ) : (
                    <>
                      <FormGroup
                        label={t('global.createEventDialog.form.venueLabel')}
                      >
                        <LocationSearchInput
                          onSelect={onLocationSelect}
                          value={venue}
                          onChange={(value) => setVenue(value)}
                          onBlur={onLocationBlur}
                          extraLocations={favLocations}
                        />
                      </FormGroup>

                      <Spacing size={20} />
                      <div className="create-event-modal__field-wrapper">
                        <InputField
                          name="adressName"
                          label={t(
                            'global.createEventDialog.form.locationName'
                          )}
                          maxChars={100}
                          validate={required(
                            t(
                              'global.createEventDialog.form.locationNameRequired'
                            )
                          )}
                        />
                        <Spacing size={20} />
                        <InputField
                          name="adressComplement"
                          label={t('global.createEventDialog.form.complement')}
                          maxChars={100}
                          optional
                          optionalLabelBelow
                        />
                      </div>
                    </>
                  )}

                  <Spacing size={20} />
                  <Checkbox
                    label={t(
                      `global.createEventDialog.form.tobeannounced.${
                        isOnline ? 'online' : 'inPerson'
                      }`
                    )}
                    checked={toBeAnnounced}
                    onChange={(v) => setToBeAnnounced(v)}
                  />
                  {toBeAnnounced && (
                    <>
                      <Spacing size={20} />
                      <div
                        className="create-event-modal__warning"
                        dangerouslySetInnerHTML={{
                          __html: t(
                            `global.createEventDialog.form.tobeannouncedWarning.${
                              isOnline ? 'online' : 'inPerson'
                            }`
                          ),
                        }}
                      />
                    </>
                  )}
                </div>

                <Spacing size={24} />

                <div className={'create-event-modal__box'}>
                  <div className="create-event-modal__field-wrapper">
                    <FormGroup
                      label={t('global.createEventDialog.form.startsAt')}
                    >
                      <InlineDateTimeInput
                        minDate={today}
                        onChange={(date) => onDateChange('startsAt', date)}
                        value={eventDates.startsAt}
                      />
                    </FormGroup>

                    <Spacing size={20} />
                    <FormGroup
                      label={t('global.createEventDialog.form.endsAt')}
                    >
                      <InlineDateTimeInput
                        initialMonth={eventDates.endsAt || eventDates.startsAt}
                        minDate={today}
                        onChange={(date) => onDateChange('endsAt', date)}
                        value={eventDates.endsAt}
                      />
                    </FormGroup>
                  </div>
                </div>

                <Spacing size={24} />

                <div className={'create-event-modal__box'}>
                  <RadioFields
                    name="eventType"
                    helperTextPosition="below"
                    label={t('global.createEventDialog.form.type')}
                    validate={required(
                      t('global.createEventDialog.form.typeRequired')
                    )}
                    options={[
                      {
                        label: t('global.privacyStateEventPublic'),
                        value: PUBLIC_EVENT,
                        helperText: t(
                          'global.privacyStateEventPublicDescription'
                        ),
                      },
                      {
                        label: t('global.privacyStateEventPrivate'),
                        value: PRIVATE_EVENT,
                        helperText: t(
                          'global.privacyStateEventPrivateDescription'
                        ),
                      },
                    ]}
                  />

                  <Spacing size={24} />
                  <div className="create-event-modal__btn-preview-field-wrapper">
                    <RadioFields
                      tip="Customize o botão do seu evento"
                      name="buttonPreview"
                      label={t('global.createEventDialog.form.buttonPreview')}
                      options={[
                        {
                          label: t(
                            'global.createEventDialog.form.buttonPreviewOptions.BUY_TICKETS'
                          ),
                          value: EventCTATypesForTicket.BUY_TICKETS,
                        },
                        {
                          label: t(
                            'global.createEventDialog.form.buttonPreviewOptions.SUBSCRIBE'
                          ),
                          value: EventCTATypesForTicket.SUBSCRIBE,
                        },
                        {
                          label: t(
                            'global.createEventDialog.form.buttonPreviewOptions.CUSTOM'
                          ),
                          value: EventCTATypesForTicket.CUSTOM,
                        },
                      ]}
                    />
                    <div className="create-event-modal__btn-preview-wrapper">
                      <BaseButton type="button" fullWidth colorTheme="success">
                        {buttonPreviewText || 'Seu texto aqui'}
                      </BaseButton>
                    </div>
                  </div>

                  {buttonPreview === EventCTATypesForTicket.CUSTOM && (
                    <>
                      <Spacing size={14} />
                      <InputField
                        ariaLabel="custom button text"
                        name="customButtonText"
                        maxChars={20}
                        placeholder={t(
                          'global.createEventDialog.form.customButtonTextPlaceholder'
                        )}
                        validate={required(
                          t(
                            'global.createEventDialog.form.customButtonTextRequired'
                          )
                        )}
                      />
                    </>
                  )}
                </div>

                <Spacing size={24} />
                <div className="new-form-layout__divider" />

                <ErrorsField
                  title={t('global.createEventDialog.form.errorsTitle')}
                />
                <Spacing size={12} />
              </div>

              {error && (
                <>
                  <br/>
                  <div style={{ color: 'red' }}>{error}</div>
                  <br/>
                </>
              )}

              <Spacing size={12} />

              <div className="create-event-modal__action-wrapper">
                {!skipOrganizationCreation ? (
                  <BaseButton
                    variant="outlined"
                    type="button"
                    colorTheme="dark"
                    onClick={() => backStep()}
                  >
                    Voltar
                  </BaseButton>
                ) : (
                  <div></div>
                )}
                <BaseButton loading={loading || eventCreated}>
                  Salvar e continuar
                </BaseButton>
              </div>
            </form>
          )
        }}
      />
    </>
  )
}

export default EventForm
