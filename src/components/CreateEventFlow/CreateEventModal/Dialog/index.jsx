import React, { useRef, useEffect } from 'react'
import { Dialog as BlueprintDialog } from '@blueprintjs/core'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faXmark } from '@fortawesome/free-solid-svg-icons'
import Stepper from 'src/components/Stepper'
import Spacing from 'src/components/Spacing'
import './Dialog.scss'

import classNames from 'classnames'

const Dialog = ({
  children,
  actions,
  onClose,
  title,
  maxWidth,
  width,
  noPadding,
  hideCloseBtn,
  height,
  skipOrganizationCreation,
  step,
  totalSteps,
  canOutsideClickClose,
  ...rest
}) => {
  const showActions = !!actions?.length

  const contentRef = useRef(null)

  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [step])

  return (
    <BlueprintDialog
      {...rest}
      onClose={onClose}
      className={'base-dialog'}
      style={{
        ...(maxWidth ? { '--max-width': `${maxWidth}` } : {}),
        ...(width ? { width: `${width}` } : {}),
        ...(height ? { height: `${height}` } : {}),
      }}
      canOutsideClickClose={canOutsideClickClose}
    >
      {title && (
        <div className={'base-dialog__title-box-container'}>
          <h3 className={'base-dialog__title-box-text'}>{title}</h3>
        </div>
      )}
      {onClose && !hideCloseBtn && (
        <button className="base-dialog__close-btn" onClick={onClose}>
          <FontAwesomeIcon icon={faXmark} size="lg" color="#1E2023" />
        </button>
      )}

      {!skipOrganizationCreation && step && (
        <>
          <div className="fixed-header">
            <Stepper currentStep={step} totalSteps={totalSteps} />
          </div>
        </>
      )}
      <div
        ref={contentRef}
        className={classNames(
          'base-dialog__content',
          noPadding ? 'no-padding' : '',
          step ? 'base-dialog__no-top-padding' : ''
        )}
      >
        {children}
      </div>
      {showActions && (
        <div className="base-dialog__footer">
          {actions.map(({ name, onClick, type }, i) => (
            <button
              key={i}
              className={classNames('base-dialog__action-btn', type)}
              onClick={onClick}
            >
              {name}
            </button>
          ))}
        </div>
      )}
    </BlueprintDialog>
  )
}

export default Dialog
