@import '@styles/abstracts/_variables.scss';
@import 'src/styles/abstracts/_colors';

.two-factor-create-event {
  &__logo {
    display: grid;
    place-items: center;
  }

  &__title {
    font-family: Work Sans;
    font-size: 20px;
    font-weight: 600;
    line-height: 26px;
    letter-spacing: 0em;
    text-align: center;
    color: #1e2023;
    margin: 0;
  }

  &__text {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: center;
    color: #34363c;
    margin: 0;
  }

  &__label {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0em;
    text-align: left;
    color: #34363c;
    margin: 0;
  }

  &__phone-input-wrapper {
    display: flex;
    width: 100%;
    align-items: center;

    // @media #{$on-mobile} {
    //   grid-template-columns: auto auto;
    // }
  }

  &__otp-input {
    width: 54px !important;
    height: 54px;
    border-radius: 7.78px;
    border: 0.97px solid #c2c3c7;
    background-color: #ffffff;

    font-family: Work Sans;
    font-size: 20px;
    font-weight: 400;
    line-height: 23px;
    letter-spacing: 0em;
    text-align: center;
    color: #1e2023;

    margin-right: 8px;

    &.error {
      border-color: #cf3c4f;
    }

    &:nth-child(3) {
      margin-right: 26px;
    }

    &:last-child {
      margin: 0;
    }

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    &[type='number'] {
      -moz-appearance: textfield;
    }

    @media #{$on-mobile} {
      width: 40px !important;
      height: 40px;

      &:nth-child(3) {
        margin-right: 8px;
      }
    }
  }

  &__countdown {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 600;
    line-height: 16px;
    letter-spacing: 0em;
    text-align: center;

    margin: 0;
    color: #34363c;

    & > span {
      font-size: 16px;
      line-height: 19px;
    }
  }
}

.no-spinner::-webkit-inner-spin-button,
.no-spinner::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -moz-appearance: textfield; /* Firefox */
  appearance: none; /* CSS padrão */
}

.error-message {
  color: red;
  margin-bottom: 10px;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  background-color: #ffffff !important;
  -webkit-box-shadow: 0 0 0 30px #ffffff inset !important;
  color: #4c4f54 !important;
}
