import React, { useEffect, useRef, useState } from 'react'
import Dialog from '../CreateEventModal/Dialog'
import './main.scss'
import 'src/utils/scss/form.scss'
import { useMutation, useQuery } from '@apollo/client'
import {
  sendNumberWhatsappMessageMutation,
  twoFactorViewerQuery,
  validPinWhatsappMessageMutation,
} from './queries'
import { Form } from 'react-final-form'
import {
  composeValidators,
  required,
  validPhone,
} from 'src/components/FormUtils/validators'
import { withTranslations } from 'lets-i18n'
import BaseButton from 'src/components/BaseButton/BaseButton'
import Spacing from 'src/components/Spacing/Spacing'
import ErrorsField from 'src/components/FormUtils/ErrorsField'
import OTPInput from 'react-otp-input'
import classNames from 'classnames'
import { Field } from 'react-final-form'
import InputMask from 'react-input-mask'
import { useIsMobile } from 'src/components/ScreenDetector'

const zeroPad = (num, places) => String(num).padStart(places, '0')

const ShowHide = ({ children, show }) => {
  const ref = useRef()

  return (
    <div
      ref={ref}
      style={{
        maxHeight: show ? ref?.current?.scrollHeight ?? undefined : '0px',
        overflow: 'hidden',
        transition: 'max-height 0.2s ease-out',
      }}
    >
      {children}
    </div>
  )
}

const TwoFactorDialog = ({
                           t,
                           isOpen,
                           onClose,
                           onSuccess,
                           setUserEmail,
                           onSkipOrganizationFormChange,
                         }) => {
  const { data } = useQuery(twoFactorViewerQuery)
  const [otp, setOtp] = useState('')
  const [optSent, setOtpSent] = useState(false)
  const [CDEnd, setCDEnd] = useState(false)
  const [error, setError] = useState(false)
  const [formValues, setFormValues] = useState({})
  const [errorMessage, setErrorMessage] = useState('')
  const interval = useRef(null)
  const isMobile = useIsMobile()

  useEffect(() => {
    if (isOpen) {
      setOtpSent(false)
    }
  }, [isOpen])

  useEffect(() => {
    if (data?.viewer?.email) {
      setUserEmail(data.viewer.email)
    }

    if (data?.viewer?.skip_form_first_event !== undefined) {
      onSkipOrganizationFormChange(data.viewer.skip_form_first_event)
    }
  }, [data])

  const [sendOTP, { loading: loadingSendOTP }] = useMutation(
    sendNumberWhatsappMessageMutation
  )

  const [validateOTP, { loading: loadingValidateOTP }] = useMutation(
    validPinWhatsappMessageMutation
  )

  useEffect(() => {
    return () => {
      if (interval.current) clearInterval(interval.current)
    }
  }, [])

  const stopCD = () => {
    if (interval.current) clearInterval(interval.current)
  }

  const startCD = () => {
    stopCD()
    setCDEnd(false)

    interval.current = setInterval(
      (() => {
        let count = 59
        var element = document.getElementById('two-factor-cd')

        element.innerText = `00:${zeroPad(count, 2)}`

        return () => {
          if (count > 0) count -= 1
          element.innerText = `00:${zeroPad(count, 2)}`
          if (count === 0) {
            setCDEnd(true)
            stopCD()
          }
        }
      })(),
      1000
    )
  }

  const handleFormSubmit = async (values) => {
    setFormValues(values)
    const cleanedPhone = values.phone.replace(/\D/g, '')
    const fullPhone = `+${values.countryCode}${cleanedPhone}`

    try {
      const response = await sendOTP({
        variables: {
          input: {
            whatsapp: fullPhone,
          },
        },
      })

      if (response.data.send_number_whatsapp_message.id === 'ERROR') {
        setErrorMessage(response.data.send_number_whatsapp_message.errors[0])
        return
      }

      setOtpSent(true)
      setOtp('')
      startCD()
      setError(false)
      setErrorMessage('')
    } catch (err) {
      setErrorMessage('An unexpected error occurred. Please try again.')
    }
  }

  const handleEdit = () => {
    setOtpSent(false)
  }

  const handleConfirmOTP = async () => {
    const pin = parseInt(otp)
    try {
      await validateOTP({
        variables: {
          input: {
            pin,
          },
        },
      })

      onClose()
      onSuccess()
    } catch (err) {
      setError(true)
    }
  }

  const handleNoOTP = () => {
    handleFormSubmit(formValues)
  }

  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      maxWidth={isMobile ? '90%' : '472px'}
      hideCloseBtn
    >
      <h3 className="two-factor-create-event__title">Código de verificação</h3>
      <Spacing size={16} />
      <ShowHide show={!optSent}>
        <p className="two-factor-create-event__text">
          Como este é o primeiro evento que você está criando com a conta{' '}
          {data?.viewer?.email} precisamos de mais uma validação.
        </p>
        <Spacing size={24} />{' '}
      </ShowHide>

      <p className="two-factor-create-event__label">
        {!optSent
          ? 'Informe seu WhatsApp para enviar o código de autenticação.'
          : 'Seu WhatsApp'}
      </p>
      <Spacing size={10} />
      <Form
        onSubmit={handleFormSubmit}
        render={({ handleSubmit, pristine, values, errors }) => {
          const isPhoneInvalid = !values.phone || errors.phone

          return (
            <form onSubmit={handleSubmit} className="new-form-layout">
              <div className="two-factor-create-event__phone-input-wrapper">
                <div
                  style={{
                    boxSizing: 'border-box',
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: '14px',
                    width: '422px',
                    height: '40px',
                    background: '#FFFFFF',
                    border: '1px solid #C2C3C7',
                    borderRadius: '8px',
                    padding: 0,
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: '0 12px',
                      gap: '0px',
                      height: '40px',
                      border: '1px solid #C2C3C7',
                      borderRadius: '8px',
                      background: '#FFFFFF',
                      borderLeft: 'none',
                    }}
                  >
                    <span
                      style={{
                        width: '8px',
                        fontFamily: 'Work Sans, sans-serif',
                        fontStyle: 'normal',
                        fontWeight: 400,
                        fontSize: '13px',
                        lineHeight: '16px',
                        color: '#4C4F54',
                      }}
                    >
                      +
                    </span>

                    <Field
                      name="countryCode"
                      initialValue="55"
                      validate={required(t('global.formPhoneRequired'))}
                    >
                      {({ input, meta }) => (
                        <input
                          {...input}
                          type="number"
                          min="1"
                          max="999"
                          step="1"
                          className="no-spinner"
                          style={{
                            width: '32px',
                            fontFamily: 'Work Sans, sans-serif',
                            fontSize: '13px',
                            lineHeight: '16px',
                            fontWeight: 400,
                            color: '#4C4F54',
                            background: 'transparent',
                            border: 'none',
                            outline: 'none',
                            textAlign: 'right',
                            appearance: 'none',
                            MozAppearance: 'textfield',
                            WebkitAppearance: 'none',
                          }}
                        />
                      )}
                    </Field>
                  </div>

                  <Field
                    name="phone"
                    validate={composeValidators(
                      required(t('global.formPhoneRequired')),
                      validPhone(t('global.formPhoneInvalid'))
                    )}
                  >
                    {({ input, meta }) => (
                      <InputMask
                        mask="(99) 99999-9999"
                        maskChar=""
                        {...input}
                        disabled={optSent}
                      >
                        {(maskedInputProps) => (
                          <input
                            {...maskedInputProps}
                            type="tel"
                            style={{
                              width: '150px',
                              height: '16px',
                              fontFamily: 'Work Sans, sans-serif',
                              fontStyle: 'normal',
                              fontWeight: 400,
                              fontSize: '13px',
                              lineHeight: '16px',
                              color: '#4C4F54',
                              backgroundColor: 'transparent',
                              border: 'none',
                              outline: 'none',
                            }}
                            placeholder="(00) 00000-0000"
                          />
                        )}
                      </InputMask>
                    )}
                  </Field>
                </div>

                {optSent && (
                  <BaseButton
                    type="button"
                    length="narrow"
                    variant="text"
                    onClick={handleEdit}
                  >
                    Editar
                  </BaseButton>
                )}
              </div>

              <Spacing size={24} />
              <ErrorsField />

              {errorMessage && (
                <div className="error-message">
                  {errorMessage}
                </div>
              )}

              <ShowHide show={!optSent}>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'end',
                  }}
                >
                  <BaseButton
                    loading={loadingSendOTP}
                    width="146px"
                    disabled={isPhoneInvalid}
                  >
                    Continuar
                  </BaseButton>
                </div>
              </ShowHide>
            </form>
          )
        }}
      />
      <ShowHide show={optSent}>
        <p
          className="two-factor-create-event__countdown"
          style={
            CDEnd
              ? {
                color: '#EB445A',
              }
              : {}
          }
        >
          {CDEnd ? 'O seu código expirou: ' : 'O código expira em: '}
          <span id="two-factor-cd"></span>
        </p>
        <Spacing size={24} />
        <OTPInput
          containerStyle={{
            justifyContent: 'center',
          }}
          value={otp}
          onChange={setOtp}
          numInputs={6}
          inputType="number"
          shouldAutoFocus={optSent}
          renderInput={(props) => (
            <input
              {...props}
              className={classNames(
                'two-factor-create-event__otp-input',
                CDEnd || error ? 'error' : ''
              )}
            />
          )}
        />
        <Spacing size={24} />
        <BaseButton
          fullWidth
          disabled={otp.length !== 6}
          onClick={handleConfirmOTP}
          loading={loadingValidateOTP}
        >
          Enviar e continuar
        </BaseButton>
        <Spacing size={8} />
        <BaseButton
          fullWidth
          variant="text"
          disabled={!CDEnd}
          onClick={handleNoOTP}
        >
          Receber novo código
        </BaseButton>
      </ShowHide>
    </Dialog>
  )
}

export default withTranslations(TwoFactorDialog)
