const { filter, assign, keys } = require('lodash')

/* { t: 123, o: 456 } => "?t=123&o=456" */
const convertQueryParamsObjToQueryString = (queryParamsObj) => {
  const queryParams = []

  /* Forward query params when redirecting */
  keys(queryParamsObj).forEach((paramName) => {
    queryParams.push(`${paramName}=${queryParamsObj[paramName]}`)
  })

  return queryParams.length > 0 ? `?${queryParams.join('&')}` : ''
}

/* This is the code responsible for allowing us to embed the authentication flow within another
 * page url. Examples:
 * /e/<event-slug>/tickets/authentication
 * /e/<event-slug>/tickets/authentication/return
 *
 * Trying to access this type of route will cause a server redirect to /e/<event-slug>/tickets
 * since the `/authentication*` part is routed on the client side with react-router-dom
 */
const addAuthenticationRedirects = (server) => {
  server.get(
    /(.+)\/authentication\/(return|register|request-password)/,
    (req, res) => {
      res.redirect(
        req.params['0'] + convertQueryParamsObjToQueryString(req.query)
      )
    }
  )
}

const mapPrettyUrlsToNextJsPages = (app, server, redirects, isGet = true) => {
  keys(redirects).forEach((incomingPath) => {
    const outgoingPath = redirects[incomingPath]

    // Isolates the params variables from the incoming path
    const params = filter(
      incomingPath.split(':'),
      (section) => section.substring(0, 1) !== '/'
    ).map((param) => param.split('/')[0])

    server[isGet ? 'get' : 'post'](incomingPath, (req, res) => {
      const queryParams = {}
      params.forEach((paramVariable) => {
        queryParams[paramVariable] = req.params[paramVariable]
      })

      return app.render(req, res, outgoingPath, assign(queryParams, req.query))
    })
  })
}

const addPrettyUrlMasks = (app, server) => {
  // Admin redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/admin/participants': '/admin/Participants',
    '/admin/organizer': '/admin/Organizer',
    '/admin/wallet': '/admin/Wallet',
    '/admin/configs': '/admin/Configs',
    '/admin/:viewerId/wallet': '/admin/Wallet',
    '/admin/:eventId/affiliate-programs': '/admin/Event/AffiliatePrograms',
    '/admin/:eventId/email-marketing': '/admin/Event/EmailMarketing',
    '/admin/:eventId/index': '/admin/Event/Index',
    '/admin/:eventId/edit': '/admin/Event/Edit',
    '/admin/:eventId/participants/manage': '/admin/Event/Participants/Manage',
    '/admin/:eventId/participants/emails': '/admin/Event/Participants/Emails',
    '/admin/:eventId/page': '/admin/Event/Page',
    '/admin/:eventId/promo-codes': '/admin/Event/PromoCodes',
    '/admin/:eventId/tracking-links': '/admin/Event/TrackingLinks',
    '/admin/:eventId/report/sales': '/admin/Event/Report/Sales',
    '/admin/:eventId/orders': '/admin/Event/Orders',
    '/admin/:eventId/checkin': '/admin/Event/CheckIn',
    '/admin/:eventId/tickets': '/admin/Event/Tickets',
    '/admin/:eventId/configurations': '/admin/Event/Configurations',
    '/admin/:eventId/tickets/:ticketId/participants':
      '/admin/Event/Tickets/TicketParticipants',
    '/admin/:eventId/lists': '/admin/Event/Lists',
    '/admin/:eventId/lists/:listId': '/admin/Event/Lists/Guests',
    '/admin/:eventId/team': '/admin/Event/Team',
    '/admin/events': '/admin/Events',
    '/admin/:viewerId/events': '/admin/Events',
    '/t/:t': '/shared/AcceptInvite',
    '/admin/team': '/admin/Team',
    '/admin/:eventId': '/admin/Event/Report/Sales',
    '/admin/:eventId/promotion/widget': '/admin/Event/Promotion/Widget',
    '/admin/:eventId/integrations': '/admin/Event/Integrations',
    '/admin/:eventId/report/sales-summary': '/admin/Event/Report/SalesSummary',
    '/admin/:eventId/report/nfe-report': '/admin/Event/Report/NFEReport',
    '/admin/:eventId/report/promoters': '/admin/Event/Report/Promoters',
    '/admin/:eventId/forms/lists': '/admin/Event/Forms',
    '/admin/:eventId/forms/tickets': '/admin/Event/Forms',
    '/admin/:eventId/Participants/Manage': '/admin/Event/Participants/Manage',
    '/admin/:eventId/pdv': '/admin/PDV',
  })

  // Participant redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/': '/participant/Home',
    '/encontrar-eventos': '/participant/Home',
    '/my/tickets': '/participant/MyTickets',
    '/my/tickets/:purchaseId': '/participant/MyTickets/PurchaseDetail',
    '/my/tickets/:purchaseId/contact-organizer':
      '/participant/MyTickets/ContactOrganizer',
    '/my/tickets/:purchaseId/:viewTicket':
      '/participant/MyTickets/PurchaseDetail',
    '/my/tickets/transfer/:purchaseItemId': '/participant/MyTickets/Transfer',
    '/organizer/:slug': '/PublishedEvent',
    '/e/:eventId': '/participant/Event',
    '/e/:eventId/tickets': '/participant/Event/Checkout',
    '/e/:eventId/tickets/*': '/participant/Event/Checkout',
    '/e/:eventId/lists': '/participant/Event/Participate/Lists',
    '/e/:eventId/lists/:channelId': '/participant/Event/Participate/Lists',
    '/e/:eventId/lists/:channelId/confirm':
      '/participant/Event/Participate/Lists/Confirm',
    '/e/:eventId/participation/:channelId':
      '/participant/Event/Participate/Lists/Participation',
    '/e/:eventId/plus-ones': '/participant/Event/Participate/PlusOnes',
    '/e/:eventId/plus-ones/:channelId':
      '/participant/Event/Participate/PlusOnes',
    '/e/:eventId/plus-ones/:channelId/confirm':
      '/participant/Event/Participate/PlusOnes/Confirm',
  })

  // Affiliates redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/affiliate/affiliations': '/affiliate/MyAffiliations',
  })

  // Authentication redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/authentication': '/shared/Authentication',
    '/authentication/GoogleOauth': '/shared/oauth',
    '/authentication/oauth': '/shared/oauth',
    '/authentication/:anyOtherSubPathGoesToTheSameRoute':
      '/shared/Authentication',
  })

  // Shared redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/user/profile/edit': '/shared/ProfileEdit',
  })

  // Tickets Widget redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/tickets-widget/:eventId': '/participant/Event/Participate/TicketsWidget',
  })

  // Tickets Widget Purchase post
  mapPrettyUrlsToNextJsPages(
    app,
    server,
    {
      '/e/:eventId/tickets':
        '/participant/Event/Participate/TicketsWidget/purchase',
    },
    false
  )

  // Lets.play Redirects
  mapPrettyUrlsToNextJsPages(app, server, {
    '/play/:eventId': '/play',
    '/play/:eventId/:participationId': '/play',
  })
}

exports.addUrlRedirects = (app, server) => {
  addAuthenticationRedirects(server)

  addPrettyUrlMasks(app, server)
}
