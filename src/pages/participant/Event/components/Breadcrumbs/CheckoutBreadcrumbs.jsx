import React, { useContext } from 'react'
import BreadCrumbs from './Breadcrumbs'
import { withTranslations } from 'lets-i18n'
import { CheckoutContext, STEP } from 'src/context/Checkout/CheckoutContext'
import { eventPath } from 'src/utils/routes'

const CheckoutBreadcrumbs = ({ t }) => {
  const { state, goBackToEventPage } = useContext(CheckoutContext)

  const eventName = state.event?.name

  if (state.step === STEP.Result) return null

  return (
    <BreadCrumbs
      stepList={[
        {
          name: 'Home',
          onClick: () => window.open('/', '_self'),
        },
        {
          name: eventName,
          onClick: goBackToEventPage,
        },
        {
          name: t('checkout.breadcrumbs.buyTicket'),
        },
      ]}
    />
  )
}

export default withTranslations(CheckoutBreadcrumbs)
