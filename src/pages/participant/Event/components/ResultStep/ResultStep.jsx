import { useMutation, useQuery } from '@apollo/client'
import { faFaceGrinHearts } from '@fortawesome/free-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { withTranslations } from 'lets-i18n'
import { useContext, useEffect } from 'react'
import BaseButton from 'src/components/BaseButton'
import Spacing from 'src/components/Spacing'
import { CheckoutContext } from 'src/context/Checkout/CheckoutContext'
import { fakeLinkDownload } from 'src/utils'
import { myTicketsPath } from 'src/utils/routes'
import './main.scss'
import { getPurchase, ticketDownloadMutation } from './queries'
import ResultFooter from './ResultFooter'
import { useIsMobile } from 'src/components/ScreenDetector'
import GoogleWalletButton from 'src/pages/participant/MyTickets/GoogleWalletButton'
import { google, outlook } from 'calendar-link'

const ResultStep = ({ t }) => {
  const { state, trackEvent, analyticsInstance, pushToDataLayer } =
    useContext(CheckoutContext)

  const eventName = state.event?.name
  const eventPhoto = state.event?.photo?.small_url
  const purchaseShortId = state?.purchase?.short_id
  const deliverToMail = state?.deliverToMail
  const purchaseId = state?.purchase?.id
  const isMobile = useIsMobile()

  const [ticketDownload, { loading: loadingTicketDownload }] = useMutation(
    ticketDownloadMutation
  )

  const { data } = useQuery(getPurchase, {
    variables: {
      id: purchaseId,
    },
  })

  const ticketList = data?.viewer?.ticket_purchase?.purchase_items.nodes

  const handleTicketDownload = async () => {
    const result = await ticketDownload({
      variables: {
        purchaseId,
      },
    })

    const url = result?.data?.export_purchase_tickets?.export_url

    if (url) fakeLinkDownload(url)
  }

  useEffect(() => {
    trackEvent('Purchase', {
      value: (state?.serverCartSummary?.total_buyer_price ?? 0) / 100,
      currency: 'BRL',
    })

    analyticsInstance.trackPurchase({
      purchaseId,
      ticketPurchase: {
        ...state.purchase,
        cart_summary: state?.serverCartSummary,
      },
    })

    pushToDataLayer('Lets.PurchaseFinish')
  }, [])

  const calendarEvent = {
    title: eventName,
    start: state.event.starts_at,
    end: state.event.ends_at,
  }

  const handleLink = (link) => {
    window.open(link, '__blank')
  }

  return (
    <>
      <div className="checkout-result-step__wrapper">
        <div className="checkout-result-step__content-card">
          <div className="checkout-result-step__head">
            <p className="checkout-result-step__head-title">
              {t('checkout.resultStep.purchaseCompleted')}
            </p>
            {isMobile && <Spacing size={14} />}
            <p className="checkout-result-step__head-text">
              {t('checkout.resultStep.purchaseID')}: <b>{purchaseShortId}</b>
            </p>
          </div>
          <Spacing size={24} />
          <div className="checkout-result-step__content">
            <div className="checkout-result-step__side-content fullheight">
              <img
                className="checkout-result-step__side-content-img"
                src={eventPhoto}
              />
              <Spacing size={16} />
              <p className="checkout-result-step__side-content-title">
                {eventName}
              </p>
              <Spacing size={16} />
              <BaseButton
                onClick={handleTicketDownload}
                loading={loadingTicketDownload}
                fullWidth
              >
                {t('checkout.resultStep.downloadTickets')}
              </BaseButton>
              {!isMobile && (
                <>
                  <Spacing size={16} />
                  <div className="checkout-result-step__calendar-content">
                    <p className="checkout-result-step__small-text">
                      Adicionar a um calendário
                    </p>
                    <p className="checkout-result-step__small-text">
                      <span onClick={() => handleLink(google(calendarEvent))}>
                        Google
                      </span>{' '}
                      /{' '}
                      <span onClick={() => handleLink(outlook(calendarEvent))}>
                        Outlook
                      </span>
                    </p>
                    <Spacing size={16} />
                    <GoogleWalletButton
                      event={state.event}
                      ticketList={ticketList}
                    />
                  </div>
                </>
              )}
            </div>
            <div className="checkout-result-step__message-content">
              <div className="checkout-result-step__message-content-card">
                <div className="checkout-result-step__message-thanks-wrapper">
                  <FontAwesomeIcon
                    icon={faFaceGrinHearts}
                    color="#004ED2"
                    size="2x"
                  />
                  <p className="checkout-result-step__message-thanks-title">
                    {t('checkout.resultStep.thanks.title')}
                  </p>
                </div>
                <Spacing size={16} />
                <p className="checkout-result-step__message-content-text">
                  {t('checkout.resultStep.thanks.text')}
                </p>
              </div>
              <Spacing size={24} />
              <div className="checkout-result-step__message-content-card">
                <p className="checkout-result-step__message-content-text">
                  <span>{t('checkout.resultStep.ticketAccess.p1')}</span>{' '}
                  <a href={myTicketsPath()}>
                    {t('checkout.resultStep.ticketAccess.p2')}
                  </a>{' '}
                  <span>{t('checkout.resultStep.ticketAccess.p3')}</span>{' '}
                  <b>{deliverToMail}</b>
                </p>
              </div>
            </div>
            <ResultFooter fullWidth={false} />
          </div>
        </div>
      </div>
    </>
  )
}

export default withTranslations(ResultStep)
