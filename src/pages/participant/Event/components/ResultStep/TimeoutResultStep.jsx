import { useMutation } from '@apollo/client'
import {
  faFaceGrinHearts,
  faFaceSadTear,
} from '@fortawesome/free-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { withTranslations } from 'lets-i18n'
import { useContext } from 'react'
import BaseButton from 'src/components/BaseButton'
import Spacing from 'src/components/Spacing'
import { CheckoutContext, STEP } from 'src/context/Checkout/CheckoutContext'
import { fakeLinkDownload } from 'src/utils'
import { eventPath, myTicketsPath } from 'src/utils/routes'
import './main.scss'
import { ticketDownloadMutation } from './queries'
import ResultFooter from './ResultFooter'
import { useIsMobile } from 'src/components/ScreenDetector'

const TimeoutResultStep = ({ t }) => {
  const { state, isPDV, setStep, goBackToEventPage } =
    useContext(CheckoutContext)

  const eventName = state.event?.name
  const eventPhoto = state.event?.photo?.small_url
  const purchaseShortId = state?.purchase?.short_id
  const deliverToMail = state?.deliverToMail
  const purchaseId = state?.purchase?.id
  const isMobile = useIsMobile()

  const handleBackToEventPage = () => {
    if (isPDV) {
      setStep(STEP.Ticket)
    } else {
      goBackToEventPage()
    }
  }

  return (
    <>
      <div className="checkout-result-step__wrapper">
        <div className="checkout-result-step__content-card">
          <div className="checkout-result-step__head">
            <p className="checkout-result-step__head-title">
              {t('checkout.resultStep.timeout.title')}
            </p>
            {isMobile && <Spacing size={14} />}
            <p className="checkout-result-step__head-text">
              {t('checkout.resultStep.purchaseID')}: <b>{purchaseShortId}</b>
            </p>
          </div>
          <Spacing size={24} />
          <div className="checkout-result-step__content">
            <div className="checkout-result-step__side-content">
              <img
                className="checkout-result-step__side-content-img"
                src={eventPhoto}
              />
              <Spacing size={16} />
              <p className="checkout-result-step__side-content-title">
                {eventName}
              </p>
              <Spacing size={16} />
              <BaseButton onClick={handleBackToEventPage} fullWidth>
                {t('checkout.resultStep.timeout.cta')}
              </BaseButton>
            </div>
            <div className="checkout-result-step__message-content">
              <div className="checkout-result-step__message-content-card">
                <div className="checkout-result-step__message-thanks-wrapper">
                  <FontAwesomeIcon
                    icon={faFaceSadTear}
                    color="#004ED2"
                    size="2xl"
                  />
                  <p className="checkout-result-step__message-thanks-title">
                    {t('checkout.resultStep.timeout.subTitle1')}
                  </p>
                </div>
                <Spacing size={16} />
                <p className="checkout-result-step__subtitle">
                  {t('checkout.resultStep.timeout.message1')}
                </p>
              </div>
              <Spacing size={24} />
              <div className="checkout-result-step__message-content-card">
                <p className="checkout-result-step__subtitle">
                  {t('checkout.resultStep.timeout.subTitle2')}
                </p>
                <Spacing size={16} />
                <p className="checkout-result-step__message-content-text">
                  {t('checkout.resultStep.timeout.message2')}
                </p>
              </div>
            </div>

            {/* <ResultFooter /> */}
          </div>
        </div>
      </div>
    </>
  )
}

export default withTranslations(TimeoutResultStep)
