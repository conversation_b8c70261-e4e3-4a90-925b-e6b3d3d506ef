import React, { useContext } from 'react'
import './main.scss'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeftLong } from '@fortawesome/free-solid-svg-icons'
import Spacing from 'src/components/Spacing'
import PurchaseSummary from '../PurchaseSummary'
import { CheckoutContext } from 'src/context/Checkout/CheckoutContext'
import PurchaseAgreement from '../PaymentFormStep/PurchaseAgreement'
import classNames from 'classnames'

const StepWrapper = ({
  title,
  children,
  cta,
  onClickCTA,
  ctaLoading,
  disabled,
  hideTimer,
  hideEventInfo = true,
  hideAgreement = true,
  hideDiscountCupom,
  isSeatSummary,
  hideSummary,
  marginTop,
  hideSummaryDetails,
  fakeDisabled,
  bottomContent,
  hideBackButton
}) => {
  const { setToPreviousStep } = useContext(CheckoutContext)

  return (
    <div className="event-checkout-step-wrapper__wrapper">
      <div className="event-checkout-step-wrapper__box-wrapper">
        <div className="event-checkout-step-wrapper__title-wrapper">
          {!hideBackButton &&
            <>
              <button onClick={setToPreviousStep}>
                <FontAwesomeIcon icon={faArrowLeftLong} color="#1E2023" size="xl" />
              </button>
              <Spacing size={14} horizontal />
            </>
          }
          <p className="event-checkout-step-wrapper__title">{title}</p>
        </div>
        <Spacing size={25} />
        <div className={classNames(
          "event-checkout-step-wrapper__content",
          !children ? 'fullwidth' : ''
        )}>
          {children}
          {!hideSummary && (
            <PurchaseSummary
              cta={cta}
              onClick={onClickCTA}
              ctaLoading={ctaLoading}
              disabled={disabled}
              fakeDisabled={fakeDisabled}
              hideTimer={hideTimer}
              hideEventInfo={hideEventInfo}
              hideAgreement={hideAgreement}
              hideDiscountCupom={hideDiscountCupom}
              hideDetails={hideSummaryDetails}
              isSeatSummary={isSeatSummary}
              marginTop={marginTop}
              bottomContent={bottomContent}
              fullWidth={!children}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default StepWrapper
