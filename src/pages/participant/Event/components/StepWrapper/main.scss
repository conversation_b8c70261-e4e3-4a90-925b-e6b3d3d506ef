@import '@styles/abstracts/_variables.scss';

.event-checkout-step-wrapper {
  &__wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    margin: 40px 120px;

    @media #{$on-mobile} {
      margin: 32px 0 124px 0;
    }
  }

  &__box-wrapper {
    flex: 1;
    background-color: white;
    padding: 40px 96px;
    border-radius: 8px;
    max-width: 1126px;
    min-height: calc(100vh - 180px);

    @media #{$on-mobile} {
      padding: 24px 16px;
      min-height: calc(100vh - 164px);
    }
  }

  &__title-wrapper {
    display: flex;
    align-items: center;
    justify-content: start;

    & button {
      background: none;
      border: none;
      cursor: pointer;
      margin: 0;
      padding: 0;
    }
  }

  &__title {
    margin: 0;
    font-family: Work Sans;
    font-size: 18px;
    font-weight: 600;
    line-height: 21.11px;
    text-align: left;
    color: #1e2023;
  }

  &__content {
    position: relative;
    display: grid;
    grid-template-columns: 1fr auto;
    column-gap: 30px;
    align-items: start;

    @media #{$on-mobile} {
      grid-template-columns: 1fr;
    }

    &.fullwidth {
      grid-template-columns: 1fr;
    }

  }
}