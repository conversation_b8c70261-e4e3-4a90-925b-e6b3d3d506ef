import React, { useContext, useEffect, useMemo, useRef, useState } from 'react'
import './main.scss'
import EventInfo from './components/EventInfo'
import Summary from './components/Summary'
import CountdownWrapper from './components/CountdownWrapper'
import BaseButton from 'src/components/BaseButton'
import Spacing from 'src/components/Spacing'
import { useIsMobile } from 'src/components/ScreenDetector'
import TotalPrice from './components/TotalPrice'
import Countdown from './components/Countdown'
import classNames from 'classnames'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCross, faXmark } from '@fortawesome/free-solid-svg-icons'
import { withTranslations } from 'lets-i18n'
import PurchaseAgreement from '../PaymentFormStep/PurchaseAgreement'
import { CheckoutContext } from 'src/context/Checkout/CheckoutContext'
import SeatCategories from './components/SeatCategories'
import dayjs from 'dayjs'
import LocalizedFormat from 'dayjs/plugin/localizedFormat'
import 'dayjs/locale/pt-br'
import InstallmentsValue from './components/InstallmentsValue'

dayjs.extend(LocalizedFormat)

const PurchaseSummary = ({
  cta,
  ctaLoading,
  onClick,
  disabled,
  hideTimer,
  hideEventInfo,
  t,
  hideAgreement,
  hideDiscountCupom,
  isSeatSummary,
  marginTop,
  hideDetails,
  fakeDisabled,
  language,
  bottomContent,
  fullWidth,
}) => {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false)
  const { state } = useContext(CheckoutContext)
  const mobileSummaryRef = useRef()
  const [_, setState] = useState(0)

  const eventName = state.event?.name
  const addressOneLine = state.event?.address?.one_line
  const eventDate = dayjs(state.event?.starts_at)
    .locale(language.toLowerCase())
    .format('llll')

  const purchaseList = useMemo(() => {
    if (state.serverCartSummary) {
      const serverList = state.serverCartSummary.cart_items.map(
        ({ name, quantity, unit_price }) => {
          return {
            name,
            qty: quantity,
            price: unit_price,
          }
        }
      )

      return serverList
    }

    const values = Object.values(state.cartSummary)
    const flattenedList = values.flat()

    const list = flattenedList.map(({ id, quantity, batchId }) => {
      const ticket = state.tickets.find((t) => {
        const { id: ticketID, batchId: ticketBatchID } = t
        return ticketID === id && ticketBatchID === batchId
      })

      return {
        name: ticket.name,
        qty: quantity,
        price: ticket.price,
        fee: ticket.fee,
        priceWithDiscount: ticket.priceWithDiscount,
      }
    })

    const donationList = state.donationCart.map(({ id, amount }) => {
      var donation = state.donations.find(
        ({ id: campaignID }) => campaignID === id
      )

      return {
        name: donation.name,
        qty: 1,
        price: amount,
        fee: 0,
        priceWithDiscount: amount,
      }
    })

    return [...list, ...donationList]
  }, [state])

  const totals = useMemo(() => {
    if (state.serverCartSummary) {
      return {
        purchaseTotal: state.serverCartSummary.total_buyer_price,
        feeTotal: state.serverCartSummary.total_service_fee_passed_to_buyer,
        discount: state.discountCode
          ? {
              name: state.discountCode.code,
              value: state.serverCartSummary.total_discount_amount,
            }
          : null,
      }
    }

    if (!purchaseList || purchaseList.length === 0) {
      return {
        purchaseTotal: 0,
        feeTotal: 0,
        discount: state.discountCode
          ? {
              name: state.discountCode.code,
              value: 0,
            }
          : null,
      }
    }

    const result = purchaseList.reduce(
      (prev, { qty, price, fee, priceWithDiscount }) => {
        const purchaseTotal = prev.purchaseTotal + qty * price
        const feeTotal = prev.feeTotal + qty * fee
        const totalWithDiscount =
          prev.totalWithDiscount + qty * (priceWithDiscount ?? price)

        return {
          purchaseTotal: purchaseTotal + feeTotal,
          feeTotal,
          qtyTotal: prev.qtyTotal + qty,
          totalWithDiscount: totalWithDiscount + feeTotal,
        }
      },
      { purchaseTotal: 0, feeTotal: 0, qtyTotal: 0, totalWithDiscount: 0 }
    )

    if (state.discountCode) {
      const originalPurchaseTotal = result.purchaseTotal

      result.purchaseTotal = Math.min(
        result.purchaseTotal,
        result.totalWithDiscount
      )

      result.discount = {
        name: state.discountCode.code,
        value: originalPurchaseTotal - result.purchaseTotal,
      }
    }

    return result
  }, [purchaseList])

  const { purchaseTotal, feeTotal, discount } = totals

  const isMobile = useIsMobile()

  const hideOnMobile = !purchaseList || purchaseList.length === 0

  const mobileSummaryHeight = mobileSummaryRef.current?.scrollHeight

  useEffect(() => {
    if (mobileSummaryRef.current?.scrollHeight != mobileSummaryHeight) {
      setState((c) => c + 1)
    }
  }, [bottomContent])

  if (isMobile)
    return (
      <div
        className={classNames(
          'event-checkout-purchase-summary__mobile-wrapper',
          isDetailsOpen ? 'open' : '',
          hideOnMobile ? 'hidden' : ''
        )}
        style={{
          '--height': isDetailsOpen ? '100%' : `${mobileSummaryHeight}px`,
        }}
      >
        <div
          style={
            isDetailsOpen
              ? {
                  maxHeight: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }
              : {
                  position: 'absolute',
                  left: '-1000px',
                  top: '-1000px',
                }
          }
        >
          <div className="event-checkout-purchase-summary__details-header">
            <h4 className="event-checkout-purchase-summary__details-title">
              {t('checkout.purchaseSummary.summary')}
            </h4>
            <span role="button" onClick={() => setIsDetailsOpen((c) => !c)}>
              <FontAwesomeIcon icon={faXmark} color="#1E2023" size="2x" />
            </span>
          </div>
          <div style={{ padding: '16px', overflow: 'scroll' }}>
            <CountdownWrapper hide={hideTimer} />
            <EventInfo
              eventName={eventName}
              address={addressOneLine}
              eventDate={eventDate}
            />
            {isSeatSummary ? (
              <SeatCategories discount={discount} />
            ) : (
              <Summary
                purchaseList={purchaseList}
                feeTotal={feeTotal}
                hideDiscountCupom={hideDiscountCupom}
                discount={discount}
              />
            )}
            <TotalPrice purchaseTotal={purchaseTotal} feeTotal={feeTotal} />
            <InstallmentsValue />
          </div>
        </div>
        <div
          ref={mobileSummaryRef}
          style={{
            padding: '16px',
            ...(isDetailsOpen
              ? {
                  position: 'absolute',
                  // left: '-1000px',
                  // top: '-1000px',
                  top: '-1000px',
                }
              : {}),
          }}
        >
          {!hideDetails && (
            <>
              <div className="event-checkout-purchase-summary__mobile-price-wrapper">
                {hideTimer ? <div /> : <Countdown />}
                <BaseButton
                  length="narrow"
                  noPadding
                  variant="text"
                  onClick={() => setIsDetailsOpen((c) => !c)}
                >
                  {t('checkout.purchaseSummary.openDetails')}
                </BaseButton>
              </div>
              <div className="event-checkout-purchase-summary__divider dense" />
            </>
          )}
          <div className="event-checkout-purchase-summary__mobile-price-wrapper">
            <TotalPrice
              onlyTotal
              purchaseTotal={purchaseTotal}
              feeTotal={feeTotal}
            />
            {onClick && (
              <>
                <BaseButton
                  disabled={disabled}
                  fakeDisabled={fakeDisabled}
                  fullWidth
                  onClick={onClick}
                  loading={ctaLoading}
                  width="156px"
                  dense
                >
                  {cta ?? t('checkout.purchaseSummary.continue')}
                </BaseButton>
              </>
            )}
          </div>
          <InstallmentsValue />
          {bottomContent}
        </div>
      </div>
    )

  return (
    <>
      <div
        className={classNames(
          'event-checkout-purchase-summary__wrapper',
          fullWidth ? 'fullwidth' : ''
        )}
        style={{
          marginTop,
          position: 'sticky',
          right: 0,
          top: '90px',
        }}
      >
        <div className="event-checkout-purchase-summary__card">
          <CountdownWrapper hide={hideTimer} reverse={fullWidth} />
          <EventInfo
            eventName={eventName}
            address={addressOneLine}
            eventDate={eventDate}
            hide={hideEventInfo}
          />
          {isSeatSummary ? (
            <SeatCategories discount={discount} />
          ) : (
            <Summary
              purchaseList={purchaseList}
              feeTotal={feeTotal}
              hideDiscountCupom={hideDiscountCupom}
              discount={discount}
            />
          )}
          <TotalPrice purchaseTotal={purchaseTotal} feeTotal={feeTotal} />
        </div>
        {onClick && (
          <>
            <Spacing size={16} />
            <BaseButton
              disabled={disabled}
              fakeDisabled={fakeDisabled}
              fullWidth
              onClick={onClick}
              loading={ctaLoading}
            >
              {cta ?? t('checkout.purchaseSummary.continue')}
            </BaseButton>
          </>
        )}
        {!hideAgreement && (
          <>
            <Spacing size={16} />
            <PurchaseAgreement />
          </>
        )}
        {bottomContent}
      </div>
    </>
  )
}

export default withTranslations(PurchaseSummary)
