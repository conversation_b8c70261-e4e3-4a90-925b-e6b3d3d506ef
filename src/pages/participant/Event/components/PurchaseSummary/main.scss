@import '@styles/abstracts/_variables.scss';

.event-checkout-purchase-summary {
  &__mobile-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    border: 1px solid #eaebf0;
    box-shadow: 0px -2px 12px 0px #00000029;
    background-color: white;

    // border: 2px solid red;
    padding: 0;

    transform: translateY(0);
    height: var(--height);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    &.open {
      z-index: 13;
    }

    &.hidden {
      transform: translateY(100%);
    }

    transition: height 0.2s ease-out,
    transform 0.2s ease-out;
  }

  &__wrapper {
    width: 356px;

    &.fullwidth {
      width: 100%;
    }

  }

  &__card {
    background-color: #ffffff;
    box-shadow: 0px 0px 12px 0px #00000026;
    border: 1px solid #eaebf0;
    border-radius: 8px;
    padding: 24px;
  }

  &__divider {
    background-color: #c2c3c7;
    height: 1px;
    width: 100%;
    margin: 16px 0;
    min-height: 1px;

    &.dense {
      margin: 12px 0;
    }
  }

  &__info-wrapper {
    display: grid;
    grid-auto-flow: row;
    row-gap: 8px;
  }

  &__info-title {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    text-align: left;
    color: #1e2023;
    margin: 0;
  }

  &__info-text {
    font-family: Work Sans;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    text-align: left;
    color: #4c4f54;
    margin: 0;

    &.nowrap {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  &__price-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__total-text {
    font-family: Work Sans;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
    color: #1e2023;
    margin: 0;
  }

  &__details-title {
    font-family: Work Sans;
    font-size: 18px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;
    margin: 0;
    color: #1e2023;
  }

  &__details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    height: 70px;
    border-bottom: 1px solid #c2c3c7;
    min-height: 70px;
  }

  &__discount-cupom-form {
    & input {
      border-radius: 8px !important;
      height: 40px !important;
    }
  }

  &__categories-wrapper {
    border: 1px solid #c2c3c7;
    padding: 16px;
    border-radius: 8px;
  }

  &__category-item {
    min-height: 14px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    column-gap: 8px;
    align-items: center;
  }

  &__category-divider {
    border-bottom: 1px solid #c2c3c7;
    margin: 8px 0;
  }

  &__category-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  &__seat-obj-wrapper {
    border-radius: 8px;
    border: 1px solid #c2c3c7;
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
  }

  &__seat-obj-line-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__seat-obj-color {
    border-radius: 50%;
    height: 8px;
    width: 8px;
  }

  &__seat-obj-divider {
    border-bottom: 1px solid #c2c3c7;
  }

  &__flex-wrapper {
    display: flex;
    justify-content: start;
    align-items: center;
  }

  &__seat-obj-text {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 16.42px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    margin: 0;
    color: #4c4f54;
  }

  &__seat-obj-deselect-btn {
    cursor: pointer;
    margin: 0;
    border: unset;
    background: unset;
    padding: 0;
  }

  &__mobile-price-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__installments-text {
    font-family: Work Sans;
    font-size: 14px;
    font-weight: 400;
    line-height: 16.42px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

    margin: 0;
    color: #4c4f54;
  }

  &__applied-discount-cupom-wrapper {
    flex: 1;
    display: flex;
    justify-content: start;
    align-items: center;

    border: 1px solid #24ae5b;
    height: 40px;
    padding: 0 16px;
    border-radius: 8px;

    &>p {
      font-family: Work Sans;
      font-size: 14px;
      font-weight: 400;
      line-height: 16.42px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      margin: 0;
      color: #24ae5b;
    }
  }
}