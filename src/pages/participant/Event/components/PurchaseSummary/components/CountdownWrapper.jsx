import React, { useContext, useEffect, useMemo, useRef, useState } from 'react'
import '../main.scss'
import { CheckoutContext } from 'src/context/Checkout/CheckoutContext'
import Countdown from './Countdown'
import { withTranslations } from 'lets-i18n'

const CountdownWrapper = ({ hide, reverse, t }) => {
  if (hide) return null

  return (
    <>
      <div className="event-checkout-purchase-summary__price-wrapper" style={reverse ? {
        flexDirection: "row-reverse"
      } : null}>
        <Countdown />
        <p
          className="event-checkout-purchase-summary__info-text"
          style={{ fontSize: '13px' }}
        >
          {t('checkout.purchaseSummary.countdownText')}
        </p>
      </div>
      <div className="event-checkout-purchase-summary__divider" />
    </>
  )
}

export default withTranslations(CountdownWrapper)
