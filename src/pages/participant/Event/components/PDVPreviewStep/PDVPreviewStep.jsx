import { withTranslations } from 'lets-i18n'
import React, { useContext, useState } from 'react'
import StepWrapper from '../StepWrapper'
import BaseButton from 'src/components/BaseButton'
import Spacing from 'src/components/Spacing'
import Dialog from 'src/components/CreateEventFlow/CreateEventModal/Dialog'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircleExclamation } from '@fortawesome/free-solid-svg-icons'
import './main.scss'
import { CheckoutContext, STEP } from 'src/context/Checkout/CheckoutContext'
import { useMutation } from '@apollo/client'
import PromiseNotification from 'src/components/PromiseNotification'
import { fulfillTicketPurchaseItem } from '../FormStep/TicketFormFields/queries'
import { purchaseFinishMutation } from './queries'
import PrintTicketDialog from './PrintTicketDialog'

import moment from 'moment'
import 'moment/locale/pt'
import { dateFromStringIgnoringOffset } from 'src/utils/dateTime'
import { currencyParser } from 'src/utils'
import { usePrinter } from 'src/context/PrintContext/PrintContext'
import { getTicketListESCPOSData } from './PrintTicketDialog/utils'

const parseDate = (date, language) =>
  moment(dateFromStringIgnoringOffset(date)).locale(language)

const PDVPreviewStep = ({ t, language }) => {
  const { stopTimer, setStep, state } = useContext(CheckoutContext)
  const [isConfirmationOpen, setConfirmationOpen] = useState()
  const [isPrintDialogOpen, setPrintDialogOpen] = useState()
  const [isFinalized, setFinalized] = useState()
  const [purchasedTickets, setPurchasedTickets] = useState([])
  const { print, selectedPrinter } = usePrinter()
  const [loading, setLoading] = useState()

  const togglePrintDialog = () => setPrintDialogOpen((c) => !c)

  const [purchaseFinish] = useMutation(purchaseFinishMutation)

  const [fulfillTicketPurchaseMutation] = useMutation(fulfillTicketPurchaseItem)

  const purchaseItems = state?.purchase.purchase_items.nodes

  const fakeFufillItems = async () => {
    const promiseList = purchaseItems.map(({ id }) => {
      return fulfillTicketPurchaseMutation({
        variables: {
          input: {
            id,
            participant: {
              name: 'PDV',
            },
          },
        },
      })
    })

    return Promise.all(promiseList)
  }

  const handleConfirmation = async () => {
    setLoading(true)
    try {
      await fakeFufillItems()

      const result = await PromiseNotification(
        purchaseFinish({
          variables: {
            input: {
              id: state.purchase?.id,
              payment_method: 'PDV',
            },
          },
        }),
        'Finalizando a compra...',
        'Pedido finalizado com sucesso!',
        'Erro ao finalizar o pedido.'
      )

      if (result.data.ticket_office_purchase_finish.errors) {
        setConfirmationOpen(false)
      } else {
        const tickets =
          result.data.ticket_office_purchase_finish.purchase_items.nodes

        setPurchasedTickets(
          tickets.map(
            ({
              created_at,
              participation: { id, short_id },
              ticket: {
                name: ticketName,
                event: {
                  name: eventName,
                  address,
                  starts_at,
                  contact_information: { name: organizerName },
                },
              },
              seat_id,
              price,
            }) => {
              return {
                orderNumber:
                  result.data.ticket_office_purchase_finish.short_id?.toString(),
                ticketNumber: short_id?.toString(),
                qrCode: `v1_${id?.toString()}`,
                eventName: eventName,
                address: address?.one_line,
                buyDate: parseDate(created_at, language).format('llll'),
                organizerName: organizerName,
                ticketName: ticketName,
                startDate: parseDate(starts_at, language).format('llll'),
                seatID: seat_id?.toString(),
                price: currencyParser(price),
              }
            }
          )
        )

        finalizeOrder()
      }
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const finalizeOrder = () => {
    setFinalized(true)
    stopTimer()
    setConfirmationOpen(false)
    sessionStorage.clear('seatsio')
  }

  const handlePrint = () => {
    const data = getTicketListESCPOSData(purchasedTickets)

    print(data, {
      encoding: 'Cp1252',
    })
  }

  const showPrintButton = selectedPrinter && selectedPrinter !== ''

  return (
    <>
      {/* <Dialog isOpen={isConfirmationOpen} noPadding maxWidth="440px">
        <>
          <div className="pdv-preview-step__top-wrapper">
            <FontAwesomeIcon
              icon={faCircleExclamation}
              color="#CC9A06"
              size="4x"
            />
            <Spacing size={24} />
            <h3 className="pdv-preview-step__title">
              {t('checkout.pdvPreviewStep.confirmationModal.title')}
            </h3>
            <Spacing size={4} />
            <p className="pdv-preview-step__subtitle">
              {t('checkout.pdvPreviewStep.confirmationModal.subTitle')}
            </p>
            <Spacing size={12} />
            <p className="pdv-preview-step__text">
              {t('checkout.pdvPreviewStep.confirmationModal.text')}
            </p>
          </div>
          <div className="pdv-preview-step__divider" />
          <div className="pdv-preview-step__bottom-wrapper">
            <BaseButton loading={loading} dense onClick={handleConfirmation}>
              {t('checkout.pdvPreviewStep.confirmationModal.confirm')}
            </BaseButton>
            <Spacing size={16} horizontal />
            <BaseButton
              disabled={loading}
              variant="outlined"
              colorTheme="dark"
              dense
              onClick={() => setConfirmationOpen(false)}
            >
              {t('checkout.pdvPreviewStep.confirmationModal.cancel')}
            </BaseButton>
          </div>
        </>
      </Dialog> */}
      <StepWrapper
        title={
          isFinalized
            ? t('checkout.pdvPreviewStep.success')
            : t('checkout.pdvPreviewStep.title')
        }
        hideEventInfo={false}
        hideTimer={isFinalized}
        hideBackButton={isFinalized}
        bottomContent={
          <>
            <Spacing size={16} />
            <div className="pdv-preview-step__button-wrapper">
              {isFinalized ? (
                <>
                  <BaseButton
                    disabled={loading}
                    variant="outlined"
                    colorTheme="dark"
                    fullWidth
                    onClick={() => setStep(STEP.Ticket)}
                  >
                    {t('checkout.pdvPreviewStep.newOrder')}
                  </BaseButton>
                  {showPrintButton && (
                    <>
                      <Spacing size={30} horizontal />
                      <BaseButton
                        loading={loading}
                        fullWidth
                        onClick={handlePrint}
                      >
                        {t('checkout.pdvPreviewStep.print')}
                      </BaseButton>
                    </>
                  )}
                </>
              ) : (
                <BaseButton
                  loading={loading}
                  disabled={loading}
                  fullWidth
                  onClick={handleConfirmation}
                >
                  {t('checkout.pdvPreviewStep.cta')}
                </BaseButton>
              )}
            </div>
          </>
        }
      />
    </>
  )
}

export default withTranslations(PDVPreviewStep)
