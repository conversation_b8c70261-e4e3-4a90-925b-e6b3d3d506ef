import { gql } from '@apollo/client'

export const purchaseFinishMutation = gql`
  mutation purchaseFinishMutation($input: TicketOfficePurchaseFinishInput!) {
    ticket_office_purchase_finish(input: $input) {
      ... on TicketPurchase {
        id
        state
        short_id
        state_detail
        deliver_to
        purchase_items {
          nodes {
            price
            seat_id
            created_at
            participation {
              id
              short_id
            }
            ticket {
              name
              event {
                name
                starts_at
                address {
                  one_line
                }
                contact_information {
                  name
                }
              }
            }
          }
        }
      }
      ... on ValidationErrors {
        errors
        id
      }
    }
  }
`
