import { withTranslations } from 'lets-i18n'
import React, { useEffect, useMemo, useState } from 'react'
import BaseButton from 'src/components/BaseButton'
import Dialog from 'src/components/CreateEventFlow/CreateEventModal/Dialog'
import qz from 'qz-tray'
import Spacing from 'src/components/Spacing'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircleXmark } from '@fortawesome/free-solid-svg-icons'
import Select from 'src/components/Select'
import 'src/utils/scss/form.scss'
import { getTicketESCPOSData, getTicketListESCPOSData } from './utils'

const PrintTicketDialog = ({ purchasedTickets, isOpen, onClose, t }) => {
  const [loading, setLoading] = useState()
  const [printerList, setPrinterList] = useState([])
  const [selectedPrinter, setSelectedPrinter] = useState('')

  const initConnection = async () => {
    if (qz.websocket.isActive()) return
    setLoading(true)
    try {
      await qz.websocket.connect()
      await findPrinters()
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const findPrinters = async () => {
    setLoading(true)
    try {
      var data = await qz.printers.find()
      setPrinterList(data.reverse())
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const closeConnection = async () => {
    if (!qz.websocket.isActive()) return

    try {
      await qz.websocket.disconnect()
    } catch {}
  }

  useEffect(() => {
    if (isOpen) {
      initConnection()
    } else {
      closeConnection()
    }
  }, [isOpen])

  const havePrinters = printerList.length > 0

  const printerOptions = useMemo(() => {
    return printerList.map((name) => ({ value: name, text: name }))
  }, [printerList])

  //   console.log(printerOptions)

  const printTicket = async () => {
    setLoading(true)
    try {
      const config = qz.configs.create(selectedPrinter, {
        encoding: 'Cp1252',
      })

      const data = getTicketListESCPOSData(purchasedTickets)

      await qz.print(config, data)
    } catch (err) {
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog isOpen={isOpen} noPadding>
      <>
        <div className="pdv-preview-step__top-wrapper">
          <div className="new-form-layout">
            <Select
              value={selectedPrinter}
              options={printerOptions}
              onChange={(e) => {
                setSelectedPrinter(e)
              }}
            />
          </div>
        </div>
        <div className="pdv-preview-step__divider" />
        <div className="pdv-preview-step__bottom-wrapper">
          <BaseButton
            disabled={loading}
            variant="outlined"
            colorTheme="dark"
            onClick={onClose}
            fullWidth
          >
            {t('checkout.pdvPreviewStep.printModal.cancel')}
          </BaseButton>

          <Spacing size={16} horizontal />
          <BaseButton fullWidth disabled={loading} onClick={printTicket}>
            {t('checkout.pdvPreviewStep.printModal.print')}
          </BaseButton>
        </div>
      </>
    </Dialog>
  )
}

export default withTranslations(PrintTicketDialog)
