export const getTicketESCPOSData = ({
  qrCode,
  ticketNumber,
  orderNumber,
  eventName,
  address,
  ticketName,
  buyDate,
  organizerName,
  startDate,
  seatID,
  price,
}) => {
  var dots = '\x09'
  var qr = qrCode
  // Some proprietary size calculation
  var qrLength = qr.length + 3
  var size1 = String.fromCharCode(qrLength % 256)
  var size0 = String.fromCharCode(Math.floor(qrLength / 256))

  const addInfo = (title, text, inline = false) => {
    return [
      '\x1B' + '\x45' + '\x01', // bold on
      title,
      '\x1B' + '\x45' + '\x00', // bold off
      ...(inline
        ? [': ']
        : [
            '\x0A', // line break
          ]),
      ...(text ? [text, '\x0A'] : []),
      '\x0A', // line break
    ]
  }

  const addInlineInfo = (title, text) => {
    return [
      `${title}: ${text}`,
      '\x0A', // line break
      '\x0A', // line break
    ]
  }

  const addMargin = (number = 1) => {
    const list = []
    for (let i = 0; i < number; i++) list.push('\x0A')
    return list
  }

  const data = [
    '\x1B' + '\x40', // init
    '\x1B' + '\x61' + '\x31',
    '\x1D' +
      '\x28' +
      '\x6B' +
      '\x04' +
      '\x00' +
      '\x31' +
      '\x41' +
      '\x32' +
      '\x00', // <Function 165> select the model (model 2 is widely supported)
    '\x1D' + '\x28' + '\x6B' + '\x03' + '\x00' + '\x31' + '\x43' + dots, // <Function 167> set the size of the module
    '\x1D' + '\x28' + '\x6B' + '\x03' + '\x00' + '\x31' + '\x45' + '\x30', // <Function 169> select level of error correction (48,49,50,51) printer-dependent
    '\x1D' + '\x28' + '\x6B' + size1 + size0 + '\x31' + '\x50' + '\x30' + qr, // <Function 080> send your data (testing 123) to the image storage area in the printer
    '\x1D' + '\x28' + '\x6B' + '\x03' + '\x00' + '\x31' + '\x51' + '\x30', // <Function 081> print the symbol data in the symbol storage area
    '\x1D' + '\x28' + '\x6B' + '\x03' + '\x00' + '\x31' + '\x52' + '\x30',
    ...addMargin(2),
    '\x1D' + '\x4C' + '\x50' + '\x00',
    '\x1D' + '\x57' + '\xC0' + '\x01',
    '\x1B' + '\x74' + '\x10',
    '\x1B' + '\x61' + '\x30', // left align
    ...addInfo('N° do pedido', orderNumber),
    ...addInfo('N° do ingresso', ticketNumber),
    ...(seatID ? addInfo('Lugar', seatID) : []),
    ...addInfo(eventName),
    ...addInfo('Início', startDate, true),
    ...(address ? addInfo('Nome do local', address) : []),
    ...addInfo('Tipo do ingresso', ticketName),
    ...addInfo('Valor do ingresso', price),
    '\x1B' + '\x4D' + '\x31', // small text
    ...addInlineInfo('Data da venda', buyDate),
    'O reembolso pode ser realizado apenas em casos de alteração ou cancelamento do evento com autorização da produção',
    '\x0A', // line break
    ...addMargin(),
    `Produção: ${organizerName}`,
    ...addMargin(4),
  ]

  return data
}

export const getTicketListESCPOSData = (ticketList) => {
  const data = []

  for (let i = 0; i < ticketList.length; i++) {
    const ticket = ticketList[i]
    const tData = getTicketESCPOSData(ticket)
    data.push(...tData)

    const isLast = i === ticketList.length - 1

    data.push('\x1D' + '\x56' + '\x00') // full cut (new syntax)
  }

  return data
}
