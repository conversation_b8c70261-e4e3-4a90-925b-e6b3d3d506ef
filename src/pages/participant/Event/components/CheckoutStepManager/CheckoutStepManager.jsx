import React, { useContext } from 'react'
import { CheckoutContext, STEP } from 'src/context/Checkout/CheckoutContext'
import TicketStep from '../TicketStep'
import FormStep from '../FormStep'
import InvoiceStep from '../InvoiceStep'
import PaymentStep from '../PaymentStep'
import PixFormStep from '../PaymentFormStep/PixFormStep'
import CreditFormStep from '../PaymentFormStep/CreditFormStep'
import ResultStep from '../ResultStep'
import PixResultStep from '../ResultStep/PixResultStep'
import CreditUserFormStep from '../PaymentFormStep/CreditUserFormStep'
import BankSlipFormStep from '../PaymentFormStep/BankSlipFormStep'
import BankSlipResultStep from '../ResultStep/BankSlipResultStep'
import TimeoutResultStep from '../ResultStep/TimeoutResultStep'
import InternationalPaymentStep from '../PaymentFormStep/InternationalPaymentStep'
import PDVPreviewStep from '../PDVPreviewStep'

const CheckoutStepManager = () => {
  const { state } = useContext(CheckoutContext)

  return (
    <>
      {state.step === STEP.Ticket && <TicketStep />}
      {state.step === STEP.Form && <FormStep />}
      {state.step === STEP.Invoice && <InvoiceStep />}
      {state.step === STEP.Payment && <PaymentStep />}
      {state.step === STEP.PixPaymentForm && <PixFormStep />}
      {state.step === STEP.CreditCardForm && <CreditFormStep />}
      {state.step === STEP.CreditCardOwnerForm && <CreditUserFormStep />}
      {state.step === STEP.InternationalCardForm && <InternationalPaymentStep />}
      {state.step === STEP.BankSlipForm && <BankSlipFormStep />}
      {state.step === STEP.Result && <ResultStep />}
      {state.step === STEP.PixResult && <PixResultStep />}
      {state.step === STEP.BankSlipResult && <BankSlipResultStep />}
      {state.step === STEP.TimeoutResult && <TimeoutResultStep />}
      {state.step === STEP.PDVPreview && <PDVPreviewStep />}
    </>
  )
}

export default CheckoutStepManager
