import React, { useContext, useState, useMemo, useRef, useEffect } from 'react'
import './main.scss'
import { useIsMobile } from 'src/components/ScreenDetector'
import { withTranslations } from 'lets-i18n'
import { Form } from 'react-final-form'
import StepWrapper from '../StepWrapper'
import 'src/utils/scss/form.scss'
import { currencyParser, numericWhiteList } from 'src/utils'
import InputField from 'src/components/FormUtils/InputField'
import {
  required,
  composeValidators,
  creditCardValidator,
  minValue,
  maxValue,
  cpfValidator,
  cpnjValidator,
  validPhone,
  validEmail,
  minLength,
} from 'src/components/FormUtils/validators'
import Spacing from 'src/components/Spacing'
import SelectField from 'src/components/FormUtils/SelectField'
import ErrorsField from 'src/components/FormUtils/ErrorsField'
import { CheckoutContext, STEP } from 'src/context/Checkout/CheckoutContext'
import classNames from 'classnames'
import PhoneInputField from 'src/components/FormUtils/PhoneInputField'
import { useMutation } from '@apollo/client'
import { ticketOfficePurchasePayMutation } from './queries'
import HiddenField from 'src/components/FormUtils/HiddenField'
import useMercadoPago from './utils/useMercadoPago'
import PurchaseAgreement from './PurchaseAgreement'
import useClearSale from './utils/useClearSale'
import CreditErrorDialog from './utils/CreditErrorDialog/CreditErrorDialog'
import { get, omit } from 'lodash'
import Checkbox from 'src/components/Checkbox'

const DOC_TYPE = {
  CPF: 'CPF',
  CNPJ: 'CNPJ',
}

const PROVIDER = {
  MP: 'mercado_pago_by_lets',
  PAGBANK: 'pagbank',
}

const CreditUserFormStep = ({ t }) => {
  const [errorDialogOpen, setErrorDialogOpen] = useState(false)
  const [error, setError] = useState()
  const [doctype, setDoctype] = useState(DOC_TYPE.CPF)
  const [loading, setLoading] = useState()
  const [pagseguro, setPagseguro] = useState(null)
  const [reload, setReload] = useState(false)

  const [useViewerData, setUseViewerData] = useState()
  const onClearSaleFlow = useRef(false)

  useEffect(() => {
    if (typeof PagSeguro === 'undefined') {
      setReload(true)
    } else {
      setPagseguro(PagSeguro)
    }
  }, [reload])

  const formRef = useRef()

  const { state, setStep } = useContext(CheckoutContext)

  const [purchasePay] = useMutation(ticketOfficePurchasePayMutation)

  const { createToken, setInitialMercadoPagoPublicKey } = useMercadoPago({
    formRef,
  })

  const paymentMethod = state.purchase.eligible_payment_methods.find(
    ({ payment_type }) => payment_type === 'CREDIT'
  )

  const provider = paymentMethod?.payment_provider?.name

  const {
    isClear: clearClearSale,
    status: clearSaleStatus,
    startClearSaleFlow,
    validationModal,
  } = useClearSale({
    formRef,
    setInitialMercadoPagoPublicKey,
    purchaseId: state.purchase?.id,
    setErrorDialogOpen,
  })

  const isMobile = useIsMobile()

  const initialViewerValues = useMemo(() => {
    const viewer = state.user
    if (!viewer) return {}

    let userAddress = {}
    if (get(viewer, 'addresses.nodes', []).length > 0) {
      userAddress = omit(viewer.addresses.nodes[0], ['__typename', 'one_line'])
    }

    if (userAddress?.country === 'BRA') {
      userAddress.country = 'Brasil'
    }

    return {
      ...omit(viewer, [
        'addresses',
        'list_participations',
        '__typename',
        'id',
        'first_name',
        'last_name',
        'birthday',
        'gender',
        'phone_number',
      ]),
      full_name: viewer.first_name + ' ' + viewer.last_name,
      cep: userAddress?.zip_code,
      phone: viewer?.phone_number,
    }
  }, [state])

  const handleFormSubmit = async (v) => {
    setLoading(true)

    const pagbankToken = await GetPagbankToken(v)
    const mpToken = await GetMercadoPagoToken(v)

    if (!pagbankToken && !mpToken) {
      setLoading(false)
      setErrorDialogOpen(true)
      return
    }

    PurchasePay({
      pagbankToken,
      mpToken,
      isClearSaleActive: provider === PROVIDER.MP,
      v,
    })
  }

  const GetPagbankToken = async (v) => {
    const cardNumber = v.cardNumber.replaceAll(' ', '')

    const publicKey = paymentMethod?.pagbank_public_key

    const input = {
      publicKey,
      holder: v.full_name,
      number: cardNumber,
      expMonth: v.cardExpirationMonth,
      expYear: v.cardExpirationYear,
      securityCode: v.securityCode,
    }

    if (!pagseguro) return null

    const card = pagseguro.encryptCard(input)

    const encrypted = card.encryptedCard
    const hasErrors = card.hasErrors
    const errors = card.errors

    if (hasErrors) {
      console.error(errors)
      setError(JSON.stringify(errors))
      return null
    }

    return encrypted
  }

  const GetMercadoPagoToken = async (v) => {
    const { status, response } = await createToken()

    if (status !== 200) {
      console.error(response.message)
      setError(response.message)
      return null
    }

    return response.id
  }

  const PurchasePay = async ({
    pagbankToken,
    mpToken,
    isClearSaleActive,
    v,
  }) => {
    const cardNumber = v.cardNumber.replaceAll(' ', '')

    const first6 = cardNumber.substring(0, 6)
    const last4 = cardNumber.substring(12, 16)

    const expiration = `${String(state.creditInfo.expirationMonth).padStart(
      2,
      '0'
    )}-${state.creditInfo.expirationYear}`
    const installments = parseInt(state.creditInfo.installments ?? 1)

    const paymentId = paymentMethod?.id

    const input = {
      billing: {
        payer: {
          full_name: state?.user.first_name + ' ' + state?.user.last_name,
          email: v['email'],
          identification: {
            type: doctype,
            number: v[doctype.toLowerCase()],
          },
          payer_zip_code: v['cep'],
          phone: v['phone'],
        },
        invoice_info: state.invoiceInfo ?? null,
      },
      clearsale: clearSaleStatus?.current ?? undefined,
      clear_clearsale: !!clearClearSale?.current,
      credit_card_payment: {
        pagbank_payment_token: pagbankToken,
        mp_payment_token: mpToken,
        card: {
          expiration,
          first6,
          last4,
        },
        installments,
      },
      ticket_purchase_id: state?.purchase.id,
      payment_method_id: paymentId,
    }

    try {
      const result = await purchasePay({
        variables: {
          input,
        },
      })

      if (
        isClearSaleActive &&
        !onClearSaleFlow.current &&
        result?.data?.ticket_office_purchase_pay?.id === 'ERROR' &&
        state.event.clearsale_active
      ) {
        onClearSaleFlow.current = true
        await startClearSaleFlow(
          result?.data?.ticket_office_purchase_pay?.mp_public_key
        )
        return
      }

      const errorList = result?.data?.ticket_office_purchase_pay?.errors

      if (errorList?.length) {
        setErrorDialogOpen(true)
        setError(JSON.stringify(errorList[0]))
        return
      }

      // const threeDSInfo =
      //   result?.data?.ticket_office_purchase_pay?.three_ds_info ?? {}

      // const { creq, external_resource_url } = threeDSInfo

      // if (external_resource_url !== null && creq !== null) {
      //   return
      // }

      setStep(STEP.Result)
    } catch (err) {
      setErrorDialogOpen(true)
      setError(JSON.stringify(err))
    } finally {
      setLoading(false)
    }
  }

  const initialValues = useMemo(() => {
    const userInfo = useViewerData ? initialViewerValues : { phone: '+55' }

    return {
      ...userInfo,
      cardExpirationMonth: state?.creditInfo?.expirationMonth,
      cardExpirationYear: state?.creditInfo?.expirationYear,
      securityCode: state?.creditInfo?.securityCode,
      cardNumber: state?.creditInfo?.cardNumber,
    }
  }, [state, initialViewerValues, useViewerData])

  return (
    <>
      <CreditErrorDialog
        isOpen={errorDialogOpen}
        onClose={() => setErrorDialogOpen(false)}
        error={error}
      />
      {validationModal}
      <Form
        initialValues={initialValues}
        onSubmit={handleFormSubmit}
        render={({ handleSubmit, pristine, form, values }) => {
          form.change('docType', doctype)
          var docNumber = (doctype === DOC_TYPE.CPF ? values.cpf : values.cnpj) ?? ""
          form.change('docNumber', docNumber.replaceAll('/', '').replaceAll('-', '').replaceAll('.', ''))
          return (
            <StepWrapper
              title={t('checkout.paymentStep.paymentType.creditUser')}
              onClickCTA={handleSubmit}
              disabled={pristine}
              hideAgreement={false}
              ctaLoading={loading}
              cta={t('checkout.paymentStep.form.finalize')}
            >
              <div>
                <div className="payment-form-step__warning">
                  <p
                    dangerouslySetInnerHTML={{
                      __html: t('checkout.paymentFormStep.creditUser.warning'),
                    }}
                  />
                </div>
                <Spacing size={24} />
                <div className="payment-form-step__form-section-wrapper">
                  <p className="payment-form-step__subtitle">
                    {t('checkout.paymentFormStep.creditUser.subtitle')}
                  </p>
                  <Spacing size={20} />
                  <div className="payment-form-step__toggle-btn-wrapper">
                    <button
                      className={classNames(
                        'payment-form-step__toggle-btn',
                        doctype === DOC_TYPE.CPF ? 'active' : ''
                      )}
                      onClick={() => setDoctype(DOC_TYPE.CPF)}
                    >
                      CPF
                    </button>
                    <Spacing size={20} horizontal />
                    <button
                      className={classNames(
                        'payment-form-step__toggle-btn',
                        doctype === DOC_TYPE.CNPJ ? 'active' : ''
                      )}
                      onClick={() => setDoctype(DOC_TYPE.CNPJ)}
                    >
                      CNPJ
                    </button>
                  </div>
                  <Spacing size={16} />
                  <Checkbox
                    label={t('checkout.paymentFormStep.creditUser.autoFill')}
                    checked={useViewerData}
                    onChange={() => {
                      setUseViewerData((c) => !c)
                    }}
                  />
                  <Spacing size={20} />
                  <form
                    ref={formRef}
                    className="new-form-layout new-form-layout__new-error-field"
                    onSubmit={handleSubmit}
                  >
                    <HiddenField data-checkout="cardNumber" name="cardNumber" />
                    {/* <HiddenField
                      data-checkout="cardholderName"
                      name="full_name"
                    /> */}
                    <HiddenField
                      data-checkout="cardExpirationMonth"
                      name="cardExpirationMonth"
                    />
                    <HiddenField
                      data-checkout="cardExpirationYear"
                      name="cardExpirationYear"
                    />
                    <HiddenField
                      data-checkout="securityCode"
                      name="securityCode"
                    />
                    <HiddenField
                      data-checkout="docType"
                      name="docType"
                      value={doctype}
                    />
                    <HiddenField
                      data-checkout="docNumber"
                      name="docNumber"
                      value={''}
                    />
                    <div className="payment-form-step__credit-user-wrapper">
                      <div
                        style={{
                          gridColumn: '1 / -1',
                        }}
                      >
                        <InputField
                          data-checkout="cardholderName"
                          name="full_name"
                          label={t(
                            'checkout.paymentFormStep.creditUser.cardName'
                          )}
                          validate={composeValidators(required(t('')))}
                        />
                      </div>

                      {doctype === DOC_TYPE.CPF && (
                        <InputField
                          name="cpf"
                          placeholder={'xxx.xxx.xxx-xx'}
                          whitelist={numericWhiteList()}
                          maxChars={11}
                          mask={{ 3: '.', 6: '.', 9: '-' }}
                          label={'CPF'}
                          validate={composeValidators(
                            required(t('invoiceInfo.drawer.form.cpfRequired')),
                            cpfValidator(
                              t('invoiceInfo.drawer.form.invalidCpf')
                            )
                          )}
                          rightElement={null}
                        />
                      )}

                      {doctype === DOC_TYPE.CNPJ && (
                        <InputField
                          name="cnpj"
                          placeholder={'xx.xxx.xxx/xxxx-xx'}
                          whitelist={numericWhiteList()}
                          maxChars={14}
                          mask={{ 2: '.', 5: '.', 8: '/', 12: '-' }}
                          label={'CNPJ'}
                          validate={composeValidators(
                            required(t('invoiceInfo.drawer.form.cnpjRequired')),
                            cpnjValidator(
                              t('invoiceInfo.drawer.form.invalidCnpj')
                            )
                          )}
                          rightElement={null}
                        />
                      )}

                      <PhoneInputField
                        label={'Telefone'}
                        name="phone"
                        validate={composeValidators(
                          required(t('global.formPhoneRequired')),
                          validPhone(t('global.formPhoneInvalid'))
                        )}
                      />

                      <InputField
                        name="email"
                        label={'E-mail'}
                        validate={composeValidators(
                          required(t('global.formEmailAddressRequired')),
                          validEmail(t('global.formEmailAddressValid'))
                        )}
                      />

                      <InputField
                        label="CEP"
                        name="cep"
                        placeholder="xxxxx-xxx"
                        whitelist={numericWhiteList()}
                        maxChars={8}
                        mask={{ 5: '-' }}
                        validate={composeValidators(
                          required('require cep'),
                          minLength(9, 'invalid cep')
                        )}
                        rightElement={null}
                      />
                    </div>
                  </form>
                </div>
              </div>
              {isMobile && (
                <>
                  <Spacing size={24} />
                  <PurchaseAgreement />
                </>
              )}
            </StepWrapper>
          )
        }}
      />
    </>
  )
}

export default withTranslations(CreditUserFormStep)
