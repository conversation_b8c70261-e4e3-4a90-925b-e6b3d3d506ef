import { useContext, useMemo, useRef, useEffect, useCallback } from 'react'
import { CheckoutContext } from 'src/context/Checkout/CheckoutContext'
import useScript from 'src/hooks/useScript'

const useMercadoPago = ({ formRef }) => {
  const { state } = useContext(CheckoutContext)

  const [mercadoPago, retry] = useScript(
    'https://secure.mlstatic.com/sdk/javascript/v1/mercadopago.js'
  )

  const paymentData = useMemo(() => {
    return state?.purchase?.eligible_payment_methods?.find(
      ({ payment_type }) => payment_type === 'CREDIT'
    )
  }, [state])

  const setInitialMercadoPagoPublicKey = useCallback(() => {
    const key = paymentData?.mercado_pago_public_key

    if (!key) return

    window?.Mercadopago?.setPublishableKey(key)
  }, [paymentData])

  useEffect(() => {
    mercadoPago
      .then(() => {
        setInitialMercadoPagoPublicKey()
      })
      .catch(() => {
        retry()
      })
  }, [mercadoPago, retry, setInitialMercadoPagoPublicKey])

  const createToken = async () => {
    return new Promise((resolve) => {
      mercadoPago.then(() => {
        window.Mercadopago.clearSession()
        window.Mercadopago.createToken(formRef.current, (status, response) => {
          resolve({ status, response })
        })
      })
    })
  }

  return { createToken, setInitialMercadoPagoPublicKey }
}

export default useMercadoPago
