import { useMutation } from '@apollo/client'
import { faCircleNotch } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { withTranslations } from 'lets-i18n'
import { withRouter } from 'next/router'
import { useContext, useEffect, useMemo, useRef, useState } from 'react'
import AuthenticationModal from 'src/components/AuthenticationModal'
import BaseButton from 'src/components/BaseButton'
import EmptyState from 'src/components/EmptyState'
import FieldErrorMsg from 'src/components/FormUtils/FieldErrorMsg'
import { NoResults } from 'src/components/NonIdealState'
import { useIsMobile } from 'src/components/ScreenDetector'
import Spacing from 'src/components/Spacing'
import { CheckoutContext, STEP } from 'src/context/Checkout/CheckoutContext'
import DiscountCuponButton from '../PurchaseSummary/components/DiscountCuponButton'
import StepWrapper from '../StepWrapper'
import AffiliateLinkBanner from './components/AffiliateLinkBanner'
import SeatsParticipate from './components/SeatsParticipate'
import Ticket from './components/Ticket'
import TicketCategory from './components/TicketCategory'
import { startTicketPurchaseMutation } from './queries'
import './scss/_main.scss'
import { getStartPurchaseInput } from './utils'
import Donation from './components/Ticket/Donation'

const TicketStep = ({ t, router }) => {
  const {
    state,
    setStep,
    startTimer,
    setTicketSelectedQuantity,
    updateSeatsCartSummary,
    setPurchase,
    loading,
    setServerCart,
    setDiscountCode,
    fetchEventOffer,
    setCategories,
    setSeatObjects,
    setHoldToken,
    token,
    trackEvent,
    analyticsInstance,
    isPDV,
    trackingLinkID,
    pushToDataLayer
  } = useContext(CheckoutContext)

  const [showAllTickets, setShowAllTickets] = useState(false)

  const eventId = state.event.id

  const affiliateLink = state.event?.affiliate_link

  const isSeatingChart = state.event?.seating_chart

  const [authOpen, setAuthOpen] = useState()
  const [retrySubmit, setRetrySubmit] = useState()
  const [error, setError] = useState()

  const [startPurchaseMutation, { loading: loadingStartPurchase }] =
    useMutation(startTicketPurchaseMutation)

  const packages = state.tickets.filter((ticket) => ticket.type === 'PACKAGE')

  const showPrivateTickets = !!token

  const tickets = useMemo(() => {
    return state.tickets.filter(
      (ticket) =>
        ticket.type === 'TICKET' && (showPrivateTickets || ticket.public)
    )
  }, [state.tickets])

  const categories = useMemo(() => {
    return state?.event?.categories
  }, [state.event])

  const donations = state.donations

  const { privateTickets, publicTickets, categoryList, privateCount } =
    useMemo(() => {
      if (!tickets || !categories) return {}

      let privateTickets = []
      let publicTickets = []
      const categoryObject = {}
      let privateCount = 0

      tickets.forEach((ticket) => {
        const categoryID = ticket.category?.id

        const isPublic = ticket.public

        if (!isPublic) privateCount++

        if (categoryID) {
          var prev = categoryObject[categoryID]?.[ticket.id] ?? []

          categoryObject[categoryID] = {
            ...(categoryObject[categoryID] ?? {}),
            [ticket.id]: [...prev, ticket],
          }
          return
        }

        const list = isPublic ? publicTickets : privateTickets

        list.push(ticket)
      })

      const categoryList = categories.nodes.reduce(
        (prev, { id: categoryID, tickets }) => {
          const obj = categoryObject[categoryID]

          if (!obj) return prev

          const ticketList = tickets.nodes.reduce((prev, { id: ticketID }) => {
            const entry = obj[ticketID]
            if (!entry) return prev
            return [...prev, ...entry]
          }, [])

          return [...prev, ticketList]
        },
        []
      )

      const compareFunction = (a, b) => {
        const soldOutA = a.maxSelectableQuantity === 0
        const soldOutB = b.maxSelectableQuantity === 0

        if (soldOutA === soldOutB) return 0
        if (soldOutA && !soldOutB) return 1
        return -1
      }

      publicTickets = publicTickets.sort(compareFunction)
      privateTickets = privateTickets.sort(compareFunction)

      return { privateTickets, publicTickets, categoryList, privateCount }
    }, [tickets, categories])
    
  const showPublicTickets = !token || showAllTickets || privateCount === 0
  // || privateTickets.length === 0

  const isMobile = useIsMobile()

  const holdTokenRef = useRef()

  useEffect(() => {
    if (retrySubmit && state.user) {
      setRetrySubmit(false)
      startPurchase()
      setAuthOpen(false)
    }
  }, [retrySubmit, state])

  const onLogin = () => {
    setRetrySubmit(true)
    fetchEventOffer()
  }

  useEffect(() => {
    setPurchase(null)
    setServerCart(null)
  }, [])

  const convertCardToAnalytics = () => {
    const cartSummary = state.cartSummary

    const ticketList = cartSummary['TICKET'] ?? []

    const ticketBatches = {}

    ticketList.forEach((ticket) => {
      const id = ticket.id
      const offer = state.tickets.find(({ id: ticketID }) => ticketID === id)

      ticketBatches[ticket.id] = {
        quantity: ticket.quantity,
        offer: {
          ticketName: offer.name,
          fee: offer.fee,
          price: offer.price,
          priceWithDiscount: offer.priceWithDiscount,
        },
      }
    })

    return { ticketBatches }
  }

  const startPurchase = async () => {
    if (!state.user) {
      setAuthOpen(true)
      return
    }

    const input = getStartPurchaseInput(
      state.cartSummary,
      eventId,
      state?.discountCode?.code,
      holdTokenRef.current,
      token,
      trackingLinkID,
      state.donationCart
    )

    try {
      const result = await startPurchaseMutation({
        variables: {
          input,
        },
      })

      const errors = result?.data?.ticket_office_purchase_start?.errors

      if (errors) {
        setError(errors[0])
        return
      }

      const purchase = result?.data?.ticket_office_purchase_start

      trackEvent('AddToCart')

      const selectedOffers = convertCardToAnalytics()
      const eventOffer = state.event
      const promoCode = state?.discountCode?.code ?? ''

      analyticsInstance.trackAddToCart({
        eventOffer,
        selectedOffers,
        promoCode,
      })

      pushToDataLayer("Lets.PurchaseStart")

      setPurchase(purchase)
      setDiscountCode(
        result?.data?.ticket_office_purchase_start?.discount_coupon
      )
      setServerCart(purchase?.cart_summary)

      const nextStep = isPDV ? STEP.PDVPreview : STEP.Form

      setStep(nextStep)
      if (!isSeatingChart) startTimer(60 * 15)
    } catch (err) {
      setError(err.toString())
    }
  }

  const toToNextStep = () => {
    startPurchase()
  }

  const cartItemsCount = useMemo(() => {
    var list = Object.values(state.cartSummary).flat()
    return list.length
  }, [state.cartSummary])

  const getStartQTY = (ticketID, type) => {
    return (
      state.cartSummary[type]?.find(({ id }) => ticketID === id)?.quantity ?? 0
    )
  }

  const handleNewHoldToken = (obj) => {
    const { token } = obj

    if (token !== state.holdToken) {
      setSeatObjects()
      updateSeatsCartSummary()
    }

    holdTokenRef.current = token
    setHoldToken(token)
    getToken(obj)
  }

  const getToken = async ({ token, api }) => {
    const secretKey = process.env.SEATS_IO_SECRET_KEY
    const headers = new Headers()
    headers.set('Authorization', 'Basic ' + btoa(secretKey + ':'))
    headers.set('Content-Type', 'application/json')

    try {
      const result = await fetch(
        `https://api-eu.seatsio.net/hold-tokens/${token}`,
        {
          headers,
        }
      )

      const { expiresInSeconds } = await result.json()

      startTimer(expiresInSeconds)
    } catch (err) {
      setError(err.toString())
    }
  }

  useEffect(() => {
    setSeatObjects()
    updateSeatsCartSummary()
  }, [])

  const onChangeSeatParticipate = (value, type) => {
    switch (type) {
      case 'holdToken':
        handleNewHoldToken(value)
        break
      case 'selectedOffers':
        const { offers, objects } = value
        updateSeatsCartSummary(offers)
        setSeatObjects(objects)
        break
    }
  }

  return (
    <>
      <AuthenticationModal
        isVisible={authOpen}
        didLogIn={onLogin}
        didCancelLogIn={() => setAuthOpen(false)}
      />
      <StepWrapper
        title={t('checkout.ticketStep.title')}
        onClickCTA={toToNextStep}
        ctaLoading={loading || loadingStartPurchase}
        disabled={cartItemsCount === 0 && state.donationCart.length === 0}
        hideTimer={!isSeatingChart}
        hideEventInfo={isSeatingChart}
        isSeatSummary={isSeatingChart}
        // hideSummary={loading}
        hideSummaryDetails={!isSeatingChart}
        hideBackButton={isPDV}
        marginTop={isSeatingChart ? undefined : '32px'}
        bottomContent={
          error && (
            <div>
              <Spacing size={10} />
              <FieldErrorMsg error={error} display />
            </div>
          )
        }
      >
        {isSeatingChart ? (
          <div className="ticket-step-content">
            <AffiliateLinkBanner affiliateLink={affiliateLink} />
            {!loading && isMobile && <DiscountCuponButton />}
            <SeatsParticipate
              onChangeForm={onChangeSeatParticipate}
              eventOffer={state.event}
              viewer={state.user}
              tickets={tickets}
              selectedOffers={{}}
              setCategories={setCategories}
            />
          </div>
        ) : (
          <div className="ticket-step-content">
            {loading && (
              <div className="ticket-step-content__loading">
                <EmptyState
                  icon={
                    <FontAwesomeIcon
                      icon={faCircleNotch}
                      spin
                      color="#004ed2"
                      size="2x"
                    />
                  }
                  title={t('checkout.ticketStep.loading')}
                />
              </div>
            )}
            {!loading && (
              <>
                <AffiliateLinkBanner affiliateLink={affiliateLink} />
                {!loading && isMobile && <DiscountCuponButton />}
                {Boolean(packages.length) && (
                  <>
                    <p className="ticket-step-content__session-title">
                      {t('checkout.ticketStep.packageTitle')}
                    </p>
                    <div className="ticket-step-packages">
                      {packages.map((ticket) => (
                        <Ticket
                          key={ticket.id}
                          startQty={getStartQTY(ticket.id, 'PACKAGE')}
                          onChange={(qty) =>
                            setTicketSelectedQuantity(ticket, qty)
                          }
                          ticket={ticket}
                        />
                      ))}
                    </div>
                  </>
                )}
                {Boolean(packages.length && tickets.length) && <hr />}
                {Boolean(tickets.length) && (
                  <p className="ticket-step-content__session-title">
                    {t('checkout.ticketStep.ticketsTitle')}
                  </p>
                )}
                <div className="ticket-step-tickets">
                  {categoryList.map((ticketList, i) => {
                    return (
                      <TicketCategory
                        key={i}
                        ticketList={ticketList}
                        getStartQTY={getStartQTY}
                        setTicketSelectedQuantity={setTicketSelectedQuantity}
                        showPublicTickets={showPublicTickets}
                      />
                    )
                  })}

                  {privateTickets.map((ticket) => {
                    return (
                      <Ticket
                        key={ticket.id}
                        startQty={getStartQTY(ticket.id, 'TICKET')}
                        onChange={(qty) =>
                          setTicketSelectedQuantity(ticket, qty)
                        }
                        ticket={ticket}
                      />
                    )
                  })}
                  {showPublicTickets && (
                    <>
                      {publicTickets.map((ticket) => {
                        return (
                          <Ticket
                            key={ticket.id + ticket?.batchId ?? ''}
                            startQty={getStartQTY(ticket.id, 'TICKET')}
                            onChange={(qty) =>
                              setTicketSelectedQuantity(ticket, qty)
                            }
                            ticket={ticket}
                          />
                        )
                      })}
                    </>
                  )}
                  {!showPublicTickets && (
                    <BaseButton
                      onClick={() => setShowAllTickets(true)}
                      variant="text"
                    >
                      {t('checkout.ticketStep.showAll')}
                    </BaseButton>
                  )}
                </div>
                {Boolean(
                  (packages.length || tickets.length) && donations.length
                ) && <hr />}
                {Boolean(donations.length) && (
                  <p className="ticket-step-content__session-title">
                    {t('checkout.ticketStep.donationTitle')}
                  </p>
                )}
                <div className="ticket-step-tickets">
                  {donations.map((d) => {
                    return <Donation donation={d} />
                  })}
                </div>

                {!loading &&
                  (!tickets || tickets.length === 0) &&
                  (!packages || packages.length === 0) && (
                    <NoResults
                      description={t(
                        `ticket.throughSelection.nonIdealState.noResults.message`
                      )}
                      title={t(
                        `ticket.throughSelection.nonIdealState.noResults.title`
                      )}
                      visual={
                        <img
                          src={require('assets/img/ticket-icon-EmptyState.svg')}
                        />
                      }
                    />
                  )}
              </>
            )}
          </div>
        )}
      </StepWrapper>
    </>
  )
}

export default withRouter(withTranslations(TicketStep))
