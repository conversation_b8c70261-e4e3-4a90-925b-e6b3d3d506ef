import { gql } from '@apollo/client'

const FormFragment = gql`
  fragment FormFragment on Form {
    id
    name
    terms_of_use
    additional_fields {
      address {
        requested
        mandatory
      }
      birthday {
        requested
        mandatory
      }
      cnpj {
        requested
        mandatory
      }
      company {
        requested
        mandatory
      }
      cpf {
        requested
        mandatory
      }
      email {
        requested
        mandatory
      }
      gender {
        requested
        mandatory
      }
      identity_document_number {
        requested
        mandatory
      }
      job_title {
        requested
        mandatory
      }
      linkedin {
        requested
        mandatory
      }
      phone_number {
        requested
        mandatory
      }
      website {
        requested
        mandatory
      }
      whatsapp {
        requested
        mandatory
      }
    }
    channels_selection {
      channel_ids
      selection_type
      id
    }
    questions {
      answer_mode
      choices
      id
      required
      title
    }
  }
`
const TicketPurchaseItemFragment = gql`
  fragment TicketPurchaseItemFragment on TicketPurchaseItem {
    id
    name
    price
    seat_id
    fulfilled
    belongs_to_buyer
    ownership {
      transfer_state
    }
    ticket {
      id
      name
      require_address
      require_email
      require_cpf
      require_cnpj
      require_identity_document_number
      require_phone_number
      require_birthday
      require_gender
      require_company
      require_job_title
      require_website
      require_linkedin
      require_whatsapp
      terms_of_use
    }
    form {
      ...FormFragment
    }
    form_answers {
      id
      form_question_id
      question_title
      content
    }
    participant {
      id
      birthday
      cnpj
      cpf
      email
      foreigner
      gender
      identity_document_number
      name
      phone_number
      company
      job_title
      website
      whatsapp
      foreigner
      linkedin
      address {
        one_line
        city
        complement
        country
        name
        neighborhood
        number
        state
        street
        zip_code
      }
    }
  }
  ${FormFragment}
`

export const startTicketPurchaseMutation = gql`
  ${TicketPurchaseItemFragment}

  mutation ticket_office_purchase_start(
    $input: TicketOfficePurchaseStartInput!
  ) {
    ticket_office_purchase_start(input: $input) {
      ... on ValidationErrors {
        errors
      }

      ... on TicketPurchase {
        id
        short_id

        eligible_payment_methods {
          id
          active
          max_installments
          payment_type
          installment_values
          installment_interest_rates
          custom_due_date_allowed
          custom_due_date_max_days_from_now
          mercado_pago_public_key
          pagbank_public_key
          accepts_foreign_card
          payment_provider {
            name
          }
        }

        discount_coupon {
          code
          discount_cents
          discount_percentage
        }

        cart_summary(payment_method: CREDIT) {
          total_buyer_price
          total_service_fee_passed_to_buyer
          total_discount_amount
          total_service_fee
          total_quantity

          cart_items {
            name
            quantity
            unit_price
            seats
          }
        }

        donation_items {
          nodes {
            id
          }
        }

        purchase_items {
          nodes {
            ...TicketPurchaseItemFragment
          }
        }
      }
    }
  }
`
