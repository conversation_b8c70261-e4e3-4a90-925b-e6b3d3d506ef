export const getStartPurchaseInput = (
  cart,
  eventId,
  code,
  holdToken,
  token,
  trackingLinkID,
  donationCart
) => {
  const input = {
    event_id: eventId,
    sales_channel: {
      type: 'STOREFRONT',
    },
    tracking_list: trackingLinkID,
    seats_hold_token: holdToken,
    token,
  }

  input.purchase_items =
    cart['TICKET']?.map(({ batchId, quantity, seatIds }) => ({
      batch_id: batchId,
      quantity,
      seat_ids: seatIds,
    })) ?? []

  input.purchase_packages = cart['PACKAGE']?.reduce(
    (prev, { id, quantity }) => {
      const list = []
      for (let i = 0; i < quantity; i++) {
        list.push({ package_id: id })
      }
      return [...prev, ...list]
    },
    []
  )

  input.purchase_donation_items = donationCart.map(({ id, amount }) => ({
    donation_campaign_id: id,
    amount,
  }))

  if (code) input.discount_code = code

  return input
}
