import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { withTranslations } from 'lets-i18n'
import React, { useMemo, useState } from 'react'
import { currencyParser } from 'src/utils'
import './main.scss'
import Ticket from '../Ticket'
import classNames from 'classnames'
import Spacing from 'src/components/Spacing'

const TicketCategory = ({
  ticketList,
  t,
  getStartQTY,
  setTicketSelectedQuantity,
  showPublicTickets,
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const renderTicketList = useMemo(() => {
    return ticketList.filter((ticket) => {
      return !!ticket && (showPublicTickets || !ticket.public)
    })
  }, [ticketList, showPublicTickets])

  const category = useMemo(() => {
    if (renderTicketList.length === 0) return null
    return renderTicketList[0].category
  }, [renderTicketList])

  const title = category?.name
  const description = category?.description
  const photo = category?.photo?.original_url

  const minPrice = useMemo(() => {
    let min = Number.MAX_VALUE

    renderTicketList.forEach((ticket) => {
      const price = ticket.priceWithDiscount ?? ticket.price

      if (price < min) {
        min = price
      }
    })

    return min
  }, [renderTicketList])

  if (category === null || renderTicketList.length === 0) return null

  return (
    <div className="checkout-ticket-category__wrapper">
      <div
        className={classNames(
          'checkout-ticket-category__header',
          isOpen ? 'open' : ''
        )}
      >
        {photo && (
          <>
            <img className="checkout-ticket-category__photo" src={photo} />
            <Spacing size={16} horizontal />
          </>
        )}
        <div className="checkout-ticket-category__txt-wrapper">
          <p className="checkout-ticket-category__title">{title}</p>
          <Spacing size={8} />
          <p className="checkout-ticket-category__description">{description}</p>
          <Spacing size={8} />
          <p className="checkout-ticket-category__price-txt">
            {t('checkout.ticketStep.categoryMinPrice')}{' '}
            <b>{currencyParser(minPrice)}</b>
          </p>
        </div>
        <Spacing size={16} horizontal />
        <button
          className="checkout-ticket-category__btn"
          onClick={() => setIsOpen((c) => !c)}
        >
          <FontAwesomeIcon
            icon={isOpen ? faChevronUp : faChevronDown}
            color="#1E2023"
          />
        </button>
      </div>
      <div
        className={classNames(
          'checkout-ticket-category__ticket-wrapper',
          isOpen ? 'open' : ''
        )}
      >
        <div>
          <div />
          {renderTicketList.map((ticket) => {
            return (
              <Ticket
                key={ticket.batchId ?? ticket.id}
                startQty={getStartQTY(ticket.id, 'TICKET')}
                onChange={(qty) => setTicketSelectedQuantity(ticket, qty)}
                ticket={ticket}
              />
            )
          })}
          <div />
        </div>
      </div>
    </div>
  )
}

export default withTranslations(TicketCategory)
