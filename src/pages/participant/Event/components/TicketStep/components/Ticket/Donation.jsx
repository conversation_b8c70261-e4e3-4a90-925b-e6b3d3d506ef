import { withTranslations } from 'lets-i18n'
import { useState, useRef, useEffect, useContext } from 'react'
import Counter from 'src/components/Counter'
import './scss/_main.scss'
import { useIsMobile } from 'src/components/ScreenDetector'
import { centsParser, currencyParser } from 'src/utils'
import Spacing from 'src/components/Spacing'
import BaseButton from 'src/components/BaseButton'
import Dialog from 'src/components/CreateEventFlow/CreateEventModal/Dialog'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faHandHoldingHeart } from '@fortawesome/free-solid-svg-icons'
import { Flex, Text, TextField } from '@lets-events/react'
import { Form } from 'react-final-form'
import 'src/utils/scss/form.scss'
import InputField from 'src/components/FormUtils/InputField'
import { minValue } from 'src/components/FormUtils/validators'
import { CheckoutContext } from 'src/context/Checkout/CheckoutContext'

const Donation = ({ t, onChange, donation }) => {
  const {
    description,
    minimum_donation,
    name,
    id,
    sales_state,
    public: isDonationPublic,
  } = donation

  const { state, addDonationToCart, removeDonation, editDonation } =
    useContext(CheckoutContext)

  const cart = state.donationCart

  const onCartObj = cart.find(({ id: cartID }) => cartID === id)

  // console.log(cart, !!onCartObj)

  const price = currencyParser(minimum_donation)

  const formRef = useRef(null)

  const descriptionRef = useRef(null)
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [isDescriptionOverflowing, setIsDescriptionOverflowing] =
    useState(false)

  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleToggleDescriptionExpand = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded)
  }

  useEffect(() => {
    const checkOverflow = () => {
      if (descriptionRef.current) {
        setIsDescriptionOverflowing(
          descriptionRef.current.scrollHeight >
            descriptionRef.current.clientHeight
        )
      }
    }

    checkOverflow()
    window.addEventListener('resize', checkOverflow)

    return () => {
      window.removeEventListener('resize', checkOverflow)
    }
  }, [])

  const isMobile = useIsMobile()

  const isPublic = true

  const disabled = false

  const isEdit = !!onCartObj

  const handleCancel = () => {
    if (isEdit) {
      removeDonation(id)
    }

    setIsModalOpen(false)
  }

  const handleModalCTA = () => {
    formRef.current?.submit()
  }

  const handleFormSubmit = (v) => {
    var callback = isEdit ? editDonation : addDonationToCart

    callback(id, v['donation-value'])

    setIsModalOpen(false)
  }

  const minValuePrice = minValue(
    minimum_donation,
    t('checkout.ticketStep.donation.modal.minPriceErr').replace('#VAL#', price)
  )

  return (
    <>
      <Dialog
        canOutsideClickClose
        onClose={() => setIsModalOpen(false)}
        isOpen={isModalOpen}
        actions={[
          {
            name: t(
              `checkout.ticketStep.donation.modal.${
                isEdit ? 'remove' : 'cancel'
              }`
            ),
            onClick: handleCancel,
          },
          {
            name: t(
              `checkout.ticketStep.donation.modal.${
                isEdit ? 'save' : 'addDonation'
              }`
            ),
            type: 'primary',
            onClick: handleModalCTA,
          },
        ]}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {isMobile && <Spacing size={16} />}
          <FontAwesomeIcon
            icon={faHandHoldingHeart}
            color="#004ED2"
            size="3x"
          />
          <Spacing size={16} />
          <Text typography="headline5" fontWeight={'semibold'}>
            {t(
              `checkout.ticketStep.donation.modal.${
                isEdit ? 'editTitle' : 'title'
              }`
            )}
          </Text>
          <Spacing size={16} />
          <div
            style={{
              display: 'flex',
              justifyContent: 'start',
              flexDirection: 'column',
              alignItems: 'start',
              width: '100%',
            }}
          >
            <Form
              initialValues={{
                'donation-value': onCartObj?.amount,
              }}
              onSubmit={handleFormSubmit}
              render={({ handleSubmit, form }) => {
                formRef.current = form
                document.getElementById('donation-value-field')?.focus()

                return (
                  <form
                    className="new-form-layout new-form-layout__new-error-field"
                    onSubmit={handleSubmit}
                    style={{
                      width: '100%',
                    }}
                  >
                    <InputField
                      id="donation-value-field"
                      label={t(`checkout.ticketStep.donation.modal.label`)}
                      name="donation-value"
                      placeholder="R$ 15,00"
                      parse={centsParser}
                      format={currencyParser}
                      validate={minValuePrice}
                    />
                  </form>
                )
              }}
            />
          </div>
        </div>
      </Dialog>
      <div className={`ticket ${disabled ? 'ENDED' : ''}`}>
        <div className="ticket__content">
          <div className="ticket__title-wrapper">
            <h3 className="ticket__content__title">{name}</h3>
            {!isPublic && (
              <>
                <Spacing size={10} horizontal />
                <div className="ticket__batch-badge">
                  {t('checkout.ticketStep.private')}
                </div>
              </>
            )}
          </div>
          <div className="ticket__content__description-container">
            <p
              className={`ticket__content__description ${
                isDescriptionExpanded ? 'expanded' : ''
              }`}
              ref={descriptionRef}
            >
              {description}
            </p>
            {isDescriptionOverflowing && (
              <p
                onClick={handleToggleDescriptionExpand}
                className="ticket__content__read-more"
              >
                {isDescriptionExpanded
                  ? t('checkout.ticketStep.ticket.hiddenDescription')
                  : t('checkout.ticketStep.ticket.showDescription')}
              </p>
            )}
          </div>
          <div className="ticket__content__footer">
            <div
              style={{
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'center',
              }}
            >
              <p className="ticket__content__price">
                {price}{' '}
                <span className="fee">
                  {t('checkout.ticketStep.donation.minValue')}
                </span>
              </p>
            </div>
            <BaseButton dense onClick={() => setIsModalOpen(true)}>
              {t(`checkout.ticketStep.donation.${isEdit ? 'editCta' : 'cta'}`)}
            </BaseButton>
          </div>
        </div>
      </div>
    </>
  )
}

export default withTranslations(Donation)
