import { withTranslations } from 'lets-i18n'
import { useState, useRef, useEffect } from 'react'
import Counter from 'src/components/Counter'
import './scss/_main.scss'
import { useIsMobile } from 'src/components/ScreenDetector'
import { currencyParser } from 'src/utils'
import Spacing from 'src/components/Spacing'

const Ticket = ({ ticket, startQty, t, onChange }) => {
  const {
    id,
    name: title,
    description,
    paymentSuggestion,
    paymentSuggestionInstallments,
    packageItems,
    salesState,
    type,
    maxSelectableQuantity: max,
    minSelectableQuantity: min,
    batchNumber,
    showbatchNumber,
  } = ticket

  const price = currencyParser(ticket.price)
  const priceWithDiscount = currencyParser(ticket.priceWithDiscount)
  const showDiscount = ticket.price > ticket.priceWithDiscount
  const priceTax = currencyParser(ticket.fee)

  const descriptionRef = useRef(null)
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false)
  const [isDescriptionOverflowing, setIsDescriptionOverflowing] =
    useState(false)

  const handleToggleDescriptionExpand = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded)
  }

  useEffect(() => {
    const checkOverflow = () => {
      if (descriptionRef.current) {
        setIsDescriptionOverflowing(
          descriptionRef.current.scrollHeight >
            descriptionRef.current.clientHeight
        )
      }
    }

    checkOverflow()
    window.addEventListener('resize', checkOverflow)

    return () => {
      window.removeEventListener('resize', checkOverflow)
    }
  }, [])

  const isMobile = useIsMobile()

  const currentPrice = showDiscount ? priceWithDiscount : price

  const isPublic = ticket.public === undefined || ticket.public

  const soldout = salesState === 'SOLD_OUT'

  const disabled = soldout || max === 0 || salesState !== 'ACTIVE'

  return (
    <div className={`ticket ${disabled ? 'ENDED' : ''}`}>
      <div className="ticket__content">
        <div className="ticket__title-wrapper">
          <h3 className="ticket__content__title">{title}</h3>
          {showbatchNumber && (
            <>
              <Spacing size={10} horizontal />
              <div className="ticket__batch-badge">
                {batchNumber}° {t('checkout.ticketStep.batch')}
              </div>
            </>
          )}
          {!isPublic && (
            <>
              <Spacing size={10} horizontal />
              <div className="ticket__batch-badge">
                {t('checkout.ticketStep.private')}
              </div>
            </>
          )}
        </div>
        <div className="ticket__content__description-container">
          <p
            className={`ticket__content__description ${
              isDescriptionExpanded ? 'expanded' : ''
            }`}
            ref={descriptionRef}
          >
            {description}
            {packageItems?.length > 0 &&
              packageItems.map((item) => (
                <span key={item.id}>
                  {item.quantity}x {item.ticket_name}
                </span>
              ))}
          </p>
          {isDescriptionOverflowing && (
            <p
              onClick={handleToggleDescriptionExpand}
              className="ticket__content__read-more"
            >
              {isDescriptionExpanded
                ? t('checkout.ticketStep.ticket.hiddenDescription')
                : t('checkout.ticketStep.ticket.showDescription')}
            </p>
          )}
        </div>
        <div className="ticket__content__footer">
          <p className="ticket__content__price">
            {currentPrice}
            {` `}
            {showDiscount && (
              <>
                <span className="past-price">{price}</span>
                {` `}
              </>
            )}
            <span className="fee">
              (
              {t('checkout.ticketStep.ticket.plusTaxes').replace(
                '#COUNT#',
                priceTax
              )}
              )
            </span>
            {isMobile && <br />}
            {paymentSuggestion && (
              <span className="installments">
                {` `}
                {t(
                  `checkout.ticketStep.ticket.paymentSuggestion${paymentSuggestion}`
                ).replace('#COUNT#', paymentSuggestionInstallments)}
              </span>
            )}
          </p>
          {!soldout && (
            <Counter
              max={max}
              min={min}
              defaultValue={startQty}
              onChange={(value) => onChange?.(value)}
              disabled={disabled}
            />
          )}
          {soldout && (
            <>
              <div className="ticket__batch-badge">
                {t('checkout.ticketStep.soldout')}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default withTranslations(Ticket)
