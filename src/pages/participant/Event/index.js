import React from 'react'
import LayoutNavBar from 'src/components/LayoutNavBar'
import Breadcrumbs from './components/Breadcrumbs'
import { CheckoutProvider } from 'src/context/Checkout/CheckoutContext'
import CheckoutStepManager from './components/CheckoutStepManager'
import { PrintProvider } from 'src/context/PrintContext/PrintContext'
import Head from 'next/head'

const EventPage = ({ isPDV }) => {
  return (
    <>
      <Head>
        <script src="https://assets.pagseguro.com.br/checkout-sdk-js/rc/dist/browser/pagseguro.min.js"></script>
      </Head>
      <div>
        {!isPDV && <LayoutNavBar />}
        <PrintProvider startWithOpenModal={isPDV}>
          <CheckoutProvider isPDV={isPDV}>
            {!isPDV && <Breadcrumbs />}
            <CheckoutStepManager />
          </CheckoutProvider>
        </PrintProvider>
      </div>
    </>
  )
}

export default EventPage
