import { useState } from 'react'
import { useRouter } from 'next/router'
import { useQuery, useMutation } from '@apollo/client'
import ContentContainer from 'src/components/EventManager/ContentContainer'
import EventManager from 'src/components/EventManager'
import EventManagerHeader from 'src/components/EventManager/EventManagerHeader'
import Head from 'src/components/Head'
import Redirect from 'src/components/Redirect'
import PromiseNotification from 'src/components/PromiseNotification'
import { withTranslations } from 'lets-i18n'
import { integrationsQuery, setTagMutation, eventConfigSetQuery } from './queries'
import DynamicGrid from 'src/components/DynamicGrid'
import IntegrationsFormDrawer from './components/FormDrawer'
import IntegrationsCard from './components/IntegrationsCard'
import SlugAndToken from './components/SlugAndToken'
import './scss/main.scss'

const tagsStatic = [
  {
    key: 'google_analytics',
    translationKey: 'googleAnalytics',
    iconSrc: require('assets/img/google-analytics.svg'),
  },
  {
    key: 'facebook_pixel',
    translationKey: 'facebookPixel',
    iconSrc: require('assets/img/facebook-pixel.svg'),
  },
  {
    key: 'google_ads',
    translationKey: 'googleADS',
    iconSrc: require('assets/img/google-adds.svg'),
  },
]

const Integrations = ({ t }) => {
  const router = useRouter()
  const { eventId } = router.query

  const { data: config } = useQuery(eventConfigSetQuery, { 
    variables: { eventId }
  })

  const eventSlug = config?.node?.config?.checkout?.purchase?.event_slug ?? ''
  const token = config?.node?.config?.checkout?.purchase?.api_authorization_token ?? ''

  const { data, refetch, loading } = useQuery(integrationsQuery, {
    variables: { event_id: eventId },
  })

  const [setTag] = useMutation(setTagMutation, {
    onCompleted: () => {
      refetch()
    },
  })

  const tags = data?.node?.external_tags?.nodes?.reduce((acc, item) => {
    const { name } = item
    if (!acc[name]) acc[name] = []
    acc[name].push(item)
    return acc
  }, {})

  const [integrationForm, setIntegrationForm] = useState({
    open: false,
    name: '',
    translationKey: '',
  })

  const onSwitch = (name, connected, translationKey) => () => {
    if (connected) {
      PromiseNotification(
        setTag({
          variables: {
            event_id: eventId,
            name: name.toUpperCase(),
            value: null,
          },
        }),
        t('integrations.notifications.disconnect.loading'),
        t('integrations.notifications.disconnect.success'),
        t('integrations.notifications.disconnect.fail')
      );

      return;
    }

    setIntegrationForm({
      open: true,
      name: name.toUpperCase(),
      translationKey,
    })
  }

  return (
    <Redirect.IfNotLogged>
      <Head title={t('global.page.integrations')} />

      <IntegrationsFormDrawer
        id={integrationForm.name}
        translationKey={integrationForm.translationKey}
        open={integrationForm.open}
        loading={loading}
        refetch={refetch}
        setOpen={(open) =>
          setIntegrationForm((prevState) => ({ ...prevState, open }))
        }
      />

      <EventManager>
        <EventManagerHeader sectionTitle={t('global.page.integrations')} />
        <ContentContainer>
          <DynamicGrid>
            {tagsStatic.map((tag) => (
              <IntegrationsCard
                t={t}
                key={tag.key}
                integration={tag}
                tags={tags}
                onSwitch={onSwitch}
                footer={tags?.[tag.key]?.[0].value ? `ID: ${tags?.[tag.key]?.[0].value}` : t('integrations.notInformed')}
              />
            ))}
            <IntegrationsCard 
              t={t}
              title={t('integrations.partnersIntegrations')}
              integration={{ translationKey: 'token' }}
              footer={<SlugAndToken slug={eventSlug} token={token} />}
            />
          </DynamicGrid>
        </ContentContainer>
      </EventManager>
    </Redirect.IfNotLogged>
  )
}

export default withTranslations(Integrations)
