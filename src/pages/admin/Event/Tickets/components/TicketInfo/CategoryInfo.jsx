import { withTranslations } from 'lets-i18n'
import React, { useState, useRef, useEffect } from 'react'
import './scss/main.scss'
import classNames from 'classnames'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faGripLines,
  faEllipsis,
  faAngleDown,
  faAngleUp,
} from '@fortawesome/free-solid-svg-icons'
import { Info } from './PackageInfo'
import useOnClickOutside from 'src/hooks/useClickOutside'
import Spacing from 'src/components/Spacing'
import { useDrag, useDrop } from 'react-dnd'
import TicketInfo, { TICKETDNDTYPE } from './TicketInfo'
import { TICKET_TYPE } from '../../NewTicketDialog'
import { currencyParser } from 'src/utils'
import { useIsMobile } from 'src/components/ScreenDetector'

const CategoryInfo = ({
  category,
  index,
  isLast,
  loading,
  t,
  openEditDrawer,
  handleCategoryHover,
  eventId,
  setOpenTicketFormDrawer,
  onRemoveTicket,
  onPublishUnpublish,
  onExportParticipants,
  handleTicketHover,
  handleTicketDragEnd,
  handleTicketDragStart,
  ticketList,
  handleDeleteCategory,
  categoryDragStart,
  categoryDragHover,
  categoryDragEnd,
}) => {
  const [ticketViewOpen, setTicketViewOpen] = useState()
  const [dropdownOpen, setDropdownOpen] = useState(false)

  const isMobile = useIsMobile()

  const toggleDropdown = () => setDropdownOpen((c) => !c)

  const { name, photo, id, total_raised } = category

  const totalRaised = total_raised

  const containerRef = useRef()

  useOnClickOutside(containerRef, () => {
    if (!dropdownOpen) return
    setDropdownOpen(false)
  })

  const handleDropDownOptionClick = (onClick) => () => {
    setDropdownOpen(false)
    onClick?.()
  }

  const options = [
    {
      label: t('ticketsPage.category.infoCard.edit'),
      onClick: () => openEditDrawer(true, category),
    },
    {
      label: t('ticketsPage.category.infoCard.remove'),
      color: '#CF3C4F',
      highlightColor: '#********',
      onClick: () => handleDeleteCategory(id),
    },
  ]

  const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
    type: TICKETDNDTYPE.Category,
    item: { id },
    collect(monitor) {
      return {
        isDragging: monitor.isDragging() || monitor.getItem()?.id === id,
      }
    },
    end() {
      categoryDragEnd()
    },
  }))

  useEffect(() => {
    if (isDragging) categoryDragStart(id)
  }, [isDragging])

  const [{}, categoryDrop] = useDrop(() => ({
    accept: TICKETDNDTYPE.Category,
    hover(item, _) {
      if (item.id === id) return
      categoryDragHover(id, item.id)
    },
  }))

  const [{ isOver }, ticketDrop] = useDrop(() => ({
    accept: TICKETDNDTYPE.Ticket,
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
    hover(item, _) {
      handleCategoryHover(id, item.id)
    },
  }))

  useEffect(() => {
    if (isOver) setTicketViewOpen(true)
  }, [isOver])

  const actions = (
    <div className="ticket-info__actions">
      <div ref={containerRef} style={{ position: 'relative' }}>
        <button onClick={toggleDropdown} className="ticket-info__icon-btn">
          <FontAwesomeIcon icon={faEllipsis} color="#fff" size="xl" />
        </button>
        <div
          className={classNames(
            'ticket-info__dropdown',
            dropdownOpen ? 'open' : ''
          )}
        >
          {options?.map(
            ({ label, onClick, disabled, color, highlightColor }) => (
              <button
                key={label}
                disabled={disabled}
                className="ticket-info__dropdown-btn"
                onClick={handleDropDownOptionClick(onClick)}
                style={{
                  '--btn-color': color,
                  '--highlightColor': highlightColor,
                }}
              >
                {label}
              </button>
            )
          )}
        </div>
      </div>
      <button
        className="ticket-info__icon-btn secondary"
        onClick={() => setTicketViewOpen((c) => !c)}
      >
        <FontAwesomeIcon
          style={{ position: 'relative', top: '2px' }}
          icon={ticketViewOpen ? faAngleUp : faAngleDown}
          color="#fff"
          size="lg"
        />
      </button>
    </div>
  )

  const namePhoto = (
    <div style={{ display: 'flex', flex: 1 }}>
      {photo?.thumb_url && (
        <>
          <div
            className="ticket-info__thumb-photo"
            style={{ backgroundImage: `url(${photo.original_url})` }}
          />
          <Spacing size={20} horizontal />
        </>
      )}
      <Info title={t('ticketsPage.category.infoCard.name')} text={name} />
    </div>
  )

  const totalTickets = (
    <Info
      title={t('ticketsPage.category.infoCard.totalTickets')}
      text={`${ticketList.length} ${t(
        `ticketsPage.category.infoCard.${
          ticketList.length === 1 ? 'singularTicket' : 'pluralTicket'
        }`
      )}`}
    />
  )

  const revenue = (
    <Info
      title={t('ticketsPage.category.infoCard.totalRevenue')}
      text={currencyParser(totalRaised, true)}
      backgroundColor="#6CE99D88"
    />
  )

  const ticketListRender =
    ticketList.length > 0 && ticketViewOpen ? (
      <div
        className={classNames(
          'ticket-info__category-ticket-wrapper',
          isLast ? 'last' : ''
        )}
      >
        {ticketList.map((ticket, index) => {
          return (
            <TicketInfo
              contained
              loading={loading}
              isLast={index === ticketList.length - 1}
              index={index}
              key={ticket.id}
              ticket={ticket}
              eventId={eventId}
              onEditTicket={() => {
                const batches = ticket?.batches?.nodes ?? []
                let type = TICKET_TYPE.SINGLE
                if (batches[0] && batches[0].price < 0.01) {
                  type = TICKET_TYPE.FREE
                }
                if (batches.length >= 2) {
                  type = TICKET_TYPE.BATCH
                }

                setOpenTicketFormDrawer(true, ticket.id, type)
              }}
              onRemoveTicket={() => onRemoveTicket(ticket.id)}
              onPublishUnpublish={() => onPublishUnpublish(ticket)}
              onExportParticipants={() => onExportParticipants(ticket)}
              handleTicketHover={handleTicketHover}
              handleTicketDragEnd={handleTicketDragEnd}
              handleTicketDragStart={handleTicketDragStart}
            />
          )
        })}
      </div>
    ) : null

  if (isMobile)
    return (
      <div className="ticket-info">
        <div className={classNames('ticket-info__ticket')}>
          {namePhoto}
          {actions}
        </div>
        <div className={classNames('ticket-info__ticket', 'last')}>
          {totalTickets}
          {revenue}
        </div>
        {ticketListRender}
      </div>
    )

  return (
    <div className="ticket-info">
      <div ref={categoryDrop}>
        <div ref={dragPreview}>
          <div
            ref={ticketDrop}
            className={classNames(
              'ticket-info__ticket',
              index === 0 ? 'first' : '',
              isLast ? 'last' : '',
              ticketViewOpen ? 'batches-open' : ''
            )}
          >
            <div
              style={{
                cursor: 'grab',
                width: '50px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '60px',
              }}
              ref={loading ? null : drag}
            >
              <FontAwesomeIcon icon={faGripLines} color="#4C4F54" />
            </div>
            <Spacing size={20} horizontal />
            {namePhoto}
            {totalTickets}
            {revenue}
            {actions}
          </div>
        </div>
      </div>
      {ticketListRender}
    </div>
  )
}

export default withTranslations(CategoryInfo)
