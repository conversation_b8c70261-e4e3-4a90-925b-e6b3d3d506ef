import React, { useRef, useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faEllipsis, faPencil } from '@fortawesome/free-solid-svg-icons'
import classNames from 'classnames'
import useOnClickOutside from 'src/hooks/useClickOutside'
import { withTranslations } from 'lets-i18n'
import { pushRoute, eventTicketReport } from 'src/utils/routes'

const TicketInfoActions = ({
  ticket,
  t,
  onRemoveTicket,
  onPublishUnpublish,
  onExportParticipants,
  onEditTicket,
  onDuplicateTicket,
  eventId,
}) => {
  const { id, published, committed_amount, confirmed_amount } = ticket

  const [dropdownOpen, setDropdownOpen] = useState(false)

  const toggleDropdown = () => setDropdownOpen((c) => !c)

  const containerRef = useRef()

  useOnClickOutside(containerRef, () => {
    if (!dropdownOpen) return
    setDropdownOpen(false)
  })

  const handleDropDownOptionClick = (onClick) => () => {
    setDropdownOpen(false)
    onClick?.()
  }

  const options = [
    {
      label: published
        ? t('ticketsPage.dataGrid.unpublish')
        : t('ticketsPage.dataGrid.publish'),
      onClick: () => onPublishUnpublish(),
    },
    {
      label: t('ticketsPage.dataGrid.duplicate'),
      onClick: () => onDuplicateTicket(),
    },
    {
      label: t('ticketsPage.dataGrid.export'),
      onClick: () => onExportParticipants(),
      disabled: confirmed_amount === 0,
    },
    {
      label: t('ticketsPage.dataGrid.print'),
      onClick: () => pushRoute({ path: eventTicketReport(eventId, id) }),
      disabled: confirmed_amount === 0,
    },
    {
      label: t('ticketsPage.dataGrid.remove'),
      onClick: () => onRemoveTicket(),
      disabled: committed_amount > 0,
      color: '#CF3C4F',
      highlightColor: '#********',
    },
  ]

  return (
    <div className="ticket-info__actions">
      <button className="ticket-info__icon-btn" onClick={onEditTicket}>
        <FontAwesomeIcon icon={faPencil} color="#fff" />
      </button>
      <div ref={containerRef} style={{ position: 'relative' }}>
        <button
          onClick={toggleDropdown}
          className="ticket-info__icon-btn secondary"
        >
          <FontAwesomeIcon icon={faEllipsis} color="#fff" size="xl" />
        </button>
        <div
          className={classNames(
            'ticket-info__dropdown',
            dropdownOpen ? 'open' : ''
          )}
        >
          {options?.map(
            ({ label, onClick, disabled, color, highlightColor }) => (
              <button
                key={label}
                disabled={disabled}
                className="ticket-info__dropdown-btn"
                onClick={handleDropDownOptionClick(onClick)}
                style={{
                  '--btn-color': color,
                  '--highlightColor': highlightColor,
                }}
              >
                {label}
              </button>
            )
          )}
        </div>
      </div>
    </div>
  )
}

export default withTranslations(TicketInfoActions)
