import React, { useEffect, useMemo, useState } from 'react'
import './scss/main.scss'
import classNames from 'classnames'
import TicketInfoName from './TicketInfoName'
import TicketInfoStatus from './TicketInfoStatus'
import TicketInfoPrice from './TicketInfoPrice'
import TicketInfoSales from './TicketInfoSales'
import TicketInfoActions from './TicketInfoActions'
import TicketInfoViewBatches from './TicketInfoViewBatches'
import BatchInfo from './BatchInfo'
import { minBy, maxBy, head } from 'lodash'
import ScreenDetector from 'src/components/ScreenDetector'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faGripLines } from '@fortawesome/free-solid-svg-icons'
import Spacing from 'src/components/Spacing'
import { useDrag, useDrop } from 'react-dnd'

export const TICKETDNDTYPE = {
  Ticket: 'TICKET',
  Category: 'CATEGORY',
}

const TicketInfo = ({
  index,
  ticket,
  onRemoveTicket,
  onPublishUnpublish,
  onExportParticipants,
  onEditTicket,
  onDuplicateTicket,
  eventId,
  isLast,
  isMobile,
  handleTicketHover,
  handleTicketDragEnd,
  handleTicketDragStart,
  loading,
  contained,
}) => {
  const [batchViewOpen, setBatchViewOpen] = useState(false)

  const {
    id,
    name,
    batches,
    share_link,
    available_until,
    total_available,
    published,
    committed_amount,
    confirmed_amount,
    pending_amount,
  } = ticket

  const isSingleBatch = batches && batches.nodes.length === 1
  const current = head(batches.nodes).price
  const min = minBy(batches.nodes, 'price').price
  const max = maxBy(batches.nodes, 'price').price

  const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
    type: TICKETDNDTYPE.Ticket,
    item: { id },
    collect(monitor) {
      return {
        isDragging: monitor.isDragging() || monitor.getItem()?.id === id,
      }
    },
    end() {
      handleTicketDragEnd()
    },
  }))

  useEffect(() => {
    if (isDragging) handleTicketDragStart(id)
  }, [isDragging])

  const [{}, drop] = useDrop(() => ({
    accept: TICKETDNDTYPE.Ticket,
    hover(item, _) {
      if (item.id === id) return
      handleTicketHover(id, item.id)
    },
  }))

  const batchInfoList = useMemo(() => {
    if (isSingleBatch) return null

    let accumlatedSales = 0

    return batches.nodes.map((batch, index) => {
      const component = (
        <BatchInfo
          key={index}
          batch={batch}
          accumlatedSales={accumlatedSales}
          isLast={index === batches.nodes.length - 1}
          ticket={ticket}
          index={index}
        />
      )
      accumlatedSales += batch.total_available ?? batch.confirmed_amount
      return component
    })
  }, [batches, ticket, isSingleBatch])

  if (isMobile) {
    return (
      <div className="ticket-info">
        <div
          className={classNames(
            'ticket-info__ticket',
            contained ? 'contained' : ''
          )}
        >
          <TicketInfoName ticket={ticket} eventId={eventId} />
          <TicketInfoActions
            ticket={ticket}
            onRemoveTicket={onRemoveTicket}
            onPublishUnpublish={onPublishUnpublish}
            onExportParticipants={onExportParticipants}
            onEditTicket={onEditTicket}
            onDuplicateTicket={onDuplicateTicket}
            eventId={eventId}
          />
        </div>
        <div
          className={classNames(
            'ticket-info__ticket',
            batchViewOpen ? 'batches-open' : '',
            contained ? 'contained' : ''
          )}
        >
          {isSingleBatch ? (
            <TicketInfoStatus
              total={total_available}
              confirmed={confirmed_amount}
              availableUntil={available_until}
            />
          ) : (
            <TicketInfoViewBatches
              batches={batches.nodes}
              isToggleActive={batchViewOpen}
              toggleStatus={() => setBatchViewOpen((c) => !c)}
            />
          )}
          <TicketInfoPrice
            min={min}
            max={max}
            current={current}
            isSingleBatch={isSingleBatch}
          />
        </div>
        {batches.nodes && (
          <div
            className={classNames(
              'ticket-info__batch-list',
              batchViewOpen ? 'open' : '',
              isLast ? 'last' : '',
              contained ? 'contained' : ''
            )}
          >
            {batchInfoList}
          </div>
        )}
        <div
          className={classNames(
            'ticket-info__ticket',
            'last',
            contained ? 'contained' : ''
          )}
        >
          <TicketInfoSales
            pending={pending_amount}
            confirmed={confirmed_amount}
            total={total_available}
          />
        </div>
      </div>
    )
  }

  return (
    <div
      ref={drop}
      className="ticket-info"
      style={{
        opacity: isDragging || loading ? 0.5 : 1,
      }}
    >
      <div
        ref={dragPreview}
        className={classNames(
          'ticket-info__ticket',
          index === 0 ? 'first' : '',
          isLast ? 'last' : '',
          batchViewOpen ? 'batches-open' : ''
        )}
      >
        <div
          style={{
            cursor: 'grab',
            width: '50px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '60px',
          }}
          ref={loading ? null : drag}
        >
          <FontAwesomeIcon icon={faGripLines} color="#4C4F54" />
        </div>
        <TicketInfoName ticket={ticket} eventId={eventId} />
        {isSingleBatch ? (
          <TicketInfoStatus
            total={total_available}
            confirmed={confirmed_amount}
            availableUntil={available_until}
          />
        ) : (
          <TicketInfoViewBatches
            batches={batches.nodes}
            isToggleActive={batchViewOpen}
            toggleStatus={() => setBatchViewOpen((c) => !c)}
          />
        )}
        <TicketInfoPrice
          min={min}
          max={max}
          current={current}
          isSingleBatch={isSingleBatch}
        />
        <TicketInfoSales
          pending={pending_amount}
          confirmed={confirmed_amount}
          total={total_available}
        />
        <TicketInfoActions
          ticket={ticket}
          onRemoveTicket={onRemoveTicket}
          onPublishUnpublish={onPublishUnpublish}
          onExportParticipants={onExportParticipants}
          onDuplicateTicket={onDuplicateTicket}
          onEditTicket={onEditTicket}
          eventId={eventId}
        />
      </div>
      {batches.nodes && (
        <div
          className={classNames(
            'ticket-info__batch-list',
            batchViewOpen ? 'open' : '',
            isLast ? 'last' : ''
          )}
        >
          {batchInfoList}
        </div>
      )}
    </div>
  )
}

export default ScreenDetector(TicketInfo)
