import React from 'react'
import './scss/main.scss'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faEarthAmericas } from '@fortawesome/free-solid-svg-icons'
import classNames from 'classnames'
import moment from 'moment'
import 'moment/locale/pt'
import { withTranslations } from 'lets-i18n'

const TicketInfoSales = ({ t, pending, total, confirmed }) => {
  const progress = Math.max(
    Math.min(100, total ? (confirmed / total) * 100 : confirmed > 0 ? 100 : 0),
    0
  )

  return (
    <div className="ticket-info__sales">
      <div className="ticket-info__sales-data-wrapper">
        <p className="ticket-info__text" style={{ padding: '6px' }}>
          {t('ticketsPage.dataGrid.progressBar.soldTickets')}
        </p>
        <p
          className="ticket-info__text"
          style={{ textAlign: 'right', padding: '6px' }}
        >
          {confirmed}/{total || '∞'}
        </p>
      </div>
      <div className="ticket-info__sales-bar-background">
        <div
          className="ticket-info__sales-bar-progress"
          style={{
            '--progress': `${progress}%`,
          }}
        />
      </div>
      <div className="ticket-info__sales-data-wrapper">
        <p className="ticket-info__text" style={{ padding: '6px' }}>
          {t('ticketsPage.dataGrid.progressBar.pendingTickets')}
        </p>
        <p
          className="ticket-info__text"
          style={{ textAlign: 'right', padding: '6px' }}
        >
          {pending}
        </p>
      </div>
    </div>
  )
}
export default withTranslations(TicketInfoSales)
