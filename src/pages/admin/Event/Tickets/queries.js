import { gql } from '@apollo/client'

const ParticipationChannelFragment = gql`
  fragment ParticipationChannelFragment on ParticipationChannelInterface {
    description
    id
    name
    public
    share_link
    slug
  }
`

const AdminParticipationChannelFragment = gql`
  fragment AdminParticipationChannelFragment on ParticipationChannelAdminInterface {
    created_at
    ordering
    total_count: participations_count
    present_count
    published
  }
`

const TicketBatchFragment = gql`
  fragment TicketBatchFragment on TicketBatch {
    id
    price
    available_until
    total_available
    committed_amount
    confirmed_amount
    pending_amount
  }
`

const TicketFragment = gql`
  fragment TicketFragment on Ticket {
    ...ParticipationChannelFragment
    ...AdminParticipationChannelFragment
    available_until
    available_from
    buyer_pays_fee
    max_purchase_items
    min_purchase_items
    total_available
    committed_amount
    confirmed_amount
    pending_amount
    category {
      id
    }
    batches {
      nodes {
        ...TicketBatchFragment
      }
    }
  }
  ${ParticipationChannelFragment}
  ${AdminParticipationChannelFragment}
  ${TicketBatchFragment}
`
export const eventTicketsQuery = gql`
  query eventTicketsQuery(
    $first: Int
    $after: String
    $last: Int
    $before: String
    $eventId: ID!
    $filter: ParticipationChannelsFilter
  ) {
    event: node(id: $eventId, node_type: EVENT) {
      ... on Event {
        id
        starts_at
        ends_at
        utc_offset
        total_capacity
        packages {
          nodes {
            id
            buyer_pays_fee
            name
            price
            published
            ticket_count
            total_collected
            total_sales
            total_pending
            available_quantity
            package_available
            mixed_rate
            organizer_fee_percentage
            buyer_fee_percentage
            package_tickets {
              nodes {
                quantity
                ticket_name
                batch_id
              }
            }
          }
        }
        categories {
          nodes {
            description
            id
            name
            total_raised
            photo {
              original_url
              thumb_url
            }
            tickets {
              nodes {
                ...TicketFragment
              }
            }
          }
        }
        donation_campaigns {
          nodes {
            available_until
            available_from
            description
            id
            minimum_donation
            name
            public
            published
            share_link
            donation_items {
              total_count
              total_vendor_share
            }
          }
        }
        reports {
          tickets {
            sold_count
            total_count
            pending_count
          }
        }
        tickets(
          first: $first
          after: $after
          last: $last
          before: $before
          filter: $filter
          sort: [{ by: CHECKOUT_ORDERING, dir: ASC }, { by: NAME, dir: ASC }]
        ) {
          nodes {
            ...TicketFragment
          }
          pageInfo {
            hasNextPage
            startCursor
            endCursor
            hasPreviousPage
          }
        }
      }
    }
  }
  ${TicketFragment}
`

export const ticketRemoveMutation = gql`
  mutation ticketRemoveMutation($input: InputWithNodeTypeInput!) {
    delete_node(input: $input) {
      ... on DeletedNode {
        id
      }
    }
  }
`

export const exportParticipantsMutation = gql`
  mutation exportChannelParticipants($participation_channel_id: ID!) {
    export_channel_participants(
      input: { participation_channel_id: $participation_channel_id }
    ) {
      ... on Exported {
        export_url
      }
    }
  }
`

export const updateTicketMutation = gql`
  mutation update_ticket($input: UpdateTicketInput!) {
    update_ticket(input: $input) {
      ... on ValidationErrors {
        errors
      }
      ...TicketFragment
    }
  }
  ${TicketFragment}
`

export const duplicateTicketMutation = gql`
  mutation duplicateTicket($input: TicketDuplicateInput!) {
    ticket_duplicate(input: $input) {
      ...TicketFragment
    }
  }
  ${TicketFragment}
`
