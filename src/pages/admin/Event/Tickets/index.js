import { graphql } from '@apollo/client/react/hoc'
import { withTranslations } from 'lets-i18n'
import { flowRight, get, isEmpty } from 'lodash'
import { withRouter } from 'next/router'
import React from 'react'
import DataGrid from 'src/components/DataGrid'
import ConfirmDialog from 'src/components/Dialog/ConfirmDialog'
import EventManager from 'src/components/EventManager'
import ContentContainer from 'src/components/EventManager/ContentContainer'
import EventManagerHeader from 'src/components/EventManager/EventManagerHeader'
import Head from 'src/components/Head'
import NonIdealStates from 'src/components/NonIdealState'
import PaginationButtons from 'src/components/PaginationButtons'
import PromiseNotification from 'src/components/PromiseNotification'
import Redirect from 'src/components/Redirect'
import paginatedGraphql from 'src/hooks/paginatedGraphql'
import withDebouncedLoading from 'src/hooks/withDebouncedLoading'
import withState from 'src/hooks/withState'
import { fakeLinkDownload } from 'src/utils'
import ConfigBatchDisplay from './ConfigBatchDisplay'
import DonationCampaignDataGrid from './CreateTicketDrawer/DonationCampaign/DataGrid'
import FormsDrawer from './FormsDrawer'
import NewTicketDialog, { TICKET_TYPE } from './NewTicketDialog'
import TicketCtaDrawer from './TicketCtaDrawer'
import TicketReorder from './TicketReorder'
import {
  eventTicketsQuery,
  exportParticipantsMutation,
  ticketRemoveMutation,
  updateTicketMutation,
  duplicateTicketMutation,
} from './queries'

import ConfigEventCapacity from './ConfigEventCapacity'
import ConfigTicketCustomMessage from './ConfigTicketCustomMessage'
import ConfigTicketPackage from './ConfigTicketPackage/ConfigTicketPackage'
import { updatePackageMutation } from './ConfigTicketPackage/queries'
import CreateTicketDrawer from './CreateTicketDrawer/CreateTicketDrawer'
import CreateTicketButton from './components/CreateTicketButton'
import EventHeader from './components/EventHeader'
import HeaderToolbar from './components/HeaderToolbar'
import TicketDataWrapper from './components/TicketDataWrapper'
import TicketInfo from './components/TicketInfo'
import PackageInfo from './components/TicketInfo/PackageInfo'
import './scss/main.scss'
import VideoTutorialButton from 'src/components/VideoTutorialButton'
import Spacing from 'src/components/Spacing'
import ScreenDetector from 'src/components/ScreenDetector'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { ticketOrderMutation } from './TicketReorder/queries'
import TicketsWrapper from './TicketsWrapper'
import CategoryDrawer from './CategoryDrawer'
import CategoryWrapper from './CategoryWrapper'

class TicketsPage extends React.Component {
  state = {
    isTicketDrawerOpen: false,
    isRemoveTicketDialogOpen: false,
    isTicketCtaDrawerOpen: false,
    isNewTicketDialogOpen: false,
    isFormsDrawerOpen: false,
    isTicketReorderOpen: false,
    isOpenConfigBatchDisplay: false,
  }

  ticketOrderTimeoutDebounceID = null
  ticketOrderChanged = false

  onConfirmRemoveDialog = () => {
    const { t, ticketRemoveMutation } = this.props
    const { ticketToBeRemove } = this.state

    PromiseNotification(
      () =>
        ticketRemoveMutation({
          variables: {
            input: {
              id: ticketToBeRemove.id,
              node_type: ticketToBeRemove.type,
            },
          },
        }),
      t('ticketsPage.removeTicket.loading'),
      t('ticketsPage.removeTicket.succeed'),
      t('ticketsPage.removeTicket.failed')
    )

    this.toogleRemoveDialog()
  }

  onExportParticipants = (ticketToBeExport) => {
    const { t, exportParticipantsMutation } = this.props
    PromiseNotification(
      () => {
        const dataPromise = exportParticipantsMutation({
          variables: { participation_channel_id: ticketToBeExport.id },
        })
        dataPromise.then((result) => {
          const exportUrl = get(
            result,
            'data.export_channel_participants.export_url'
          )
          fakeLinkDownload(exportUrl)
        })
        return dataPromise
      },
      t('ticketsPage.exportTicket.loading'),
      t('ticketsPage.exportTicket.succeed'),
      t('ticketsPage.exportTicket.failed')
    )
  }

  onPublishUnpublish = (ticketToBeUpdated) => {
    const { updateTicketMutation, t } = this.props
    const published = ticketToBeUpdated.published

    PromiseNotification(
      () =>
        updateTicketMutation({
          variables: {
            input: {
              id: ticketToBeUpdated.id,
              published: !published,
            },
          },
        }),
      t(
        `ticketsPage.${published ? 'unpublishTicket' : 'publishTicket'}.loading`
      ),
      t(
        `ticketsPage.${published ? 'unpublishTicket' : 'publishTicket'}.succeed`
      ),
      t(`ticketsPage.${published ? 'unpublishTicket' : 'publishTicket'}.failed`)
    )
  }

  onDuplicateTicket = (ticketToBeUpdated) => {
    const { duplicateTicketMutation, t } = this.props

    PromiseNotification(
      () =>
        duplicateTicketMutation({
          variables: {
            input: {
              ticket_id: ticketToBeUpdated.id,
            },
          },
        }),
      t('ticketsPage.duplicateTicket.loading'),
      t('ticketsPage.duplicateTicket.succeed'),
      t('ticketsPage.duplicateTicket.failed')
    )
  }

  onPublishUnpublishPackage = (packageToUpdate) => {
    const { updatePackageMutation, t } = this.props
    const published = packageToUpdate.published

    PromiseNotification(
      () =>
        updatePackageMutation({
          variables: {
            input: {
              id: packageToUpdate.id,
              buyer_pays_fee: packageToUpdate.buyer_pays_fee,
              published: !published,
              price: packageToUpdate.price,
              name: packageToUpdate.name,
              mixed_rate: packageToUpdate.mixed_rate,
              available_quantity: packageToUpdate.available_quantity,
              buyer_fee_percentage: packageToUpdate.buyer_fee_percentage,
              organizer_fee_percentage:
                packageToUpdate.organizer_fee_percentage,
              tickets: packageToUpdate.package_tickets.nodes.map((v) => ({
                batch_id: v.batch_id,
                quantity: v.quantity,
              })),
            },
          },
        }),
      t(
        `ticketsPage.${published ? 'unpublishTicket' : 'publishTicket'}.loading`
      ),
      t(
        `ticketsPage.${published ? 'unpublishTicket' : 'publishTicket'}.succeed`
      ),
      t(`ticketsPage.${published ? 'unpublishTicket' : 'publishTicket'}.failed`)
    )
  }

  setOpenRemoveDialog = (open) => {
    this.setState({ isRemoveTicketDialogOpen: open, ticketToBeRemove: null })
  }

  setOpenFormsDrawer = (open, ticketId) => {
    this.setState({ isFormsDrawerOpen: open, ticketId })
  }

  setOpenTicketReorder = (open) => this.setState({ isOpenTicketReorder: open })

  setOpenTicketCustomMessage = (open) =>
    this.setState({ isOpenTicketCustomMessage: open })

  setOpenConfigBatchDisplay = (open) =>
    this.setState({ isOpenConfigBatchDisplay: open })

  toogleRemoveDialog = () =>
    this.setState((prevState) => ({
      isRemoveTicketDialogOpen: !prevState.isRemoveTicketDialogOpen,
      ticketToBeRemove: null,
    }))

  setOpenCtaDrawer = (open) =>
    this.setState({
      isTicketCtaDrawerOpen: open,
    })

  setOpenNewTicketDialog = (open) => {
    this.setState({
      isNewTicketDialogOpen: open,
    })
  }

  setOpenEventCapacityDrawer = (open) => {
    this.setState({
      isEventCapacityDrawerOpen: open,
    })
  }

  setOpenTicketPackageDrawer = (open, packageToEdit) => {
    this.setState({
      isTicketPackageDrawerOpen: open,
      packageToEdit,
    })
  }

  setOpenCategoryDrawer = (open, categoryToEdit) => {
    this.setState({
      isCategoryDrawerOpen: open,
      categoryToEdit,
    })
  }

  setOpenFormDrawer = (open, ticketId = null, batchType = null) => {
    this.setOpenNewTicketDialog(false)
    this.setState({
      isCreateTicketDrawerOpen: open,
      batchType,
      ticketId,
    })
  }

  onRemoveTicket = (id) => {
    this.setState({
      isRemoveTicketDialogOpen: true,
      ticketToBeRemove: {
        id,
        type: 'TICKET',
      },
    })
  }

  render() {
    const {
      data,
      debouncedLoading,
      searchQuery,
      setSearchQuery,
      nextPage,
      nextPageLoading,
      previousPage,
      previousPageLoading,
      t,
      router: {
        query: { eventId },
      },
      isMobile,
    } = this.props

    const {
      isRemoveTicketDialogOpen,
      isTicketDrawerOpen,
      isTicketCtaDrawerOpen,
      isNewTicketDialogOpen,
      isFormsDrawerOpen,
      batchType,
      ticketId,
      isOpenTicketReorder,
      isOpenConfigBatchDisplay,
      isOpenTicketCustomMessage,
      isEventCapacityDrawerOpen,
      isTicketPackageDrawerOpen,
      packageToEdit,
      isCreateTicketDrawerOpen,
      isCategoryDrawerOpen,
      categoryToEdit,
    } = this.state

    const { error, refetch, event, loading } = data

    const tickets = get(event, 'tickets.nodes', [])

    const categoryList = get(event, 'categories.nodes', [])

    const donationCampaigns = get(event, 'donation_campaigns.nodes', [])
    const ticketPackageList = get(event, 'packages.nodes', [])

    const utcOffset = get(event, 'utc_offset')
    const empty = isEmpty(tickets) && isEmpty(donationCampaigns) && !searchQuery
    const searchResultIsEmpty = tickets.length === 0 && searchQuery
    const report = get(event, 'reports.tickets', {})
    const totalCapacity = event?.total_capacity
    const trueID = get(event, 'id', 'meh')

    const { sold_count, total_count, pending_count } = report

    const errorTitle = searchResultIsEmpty
      ? t('ticketsPage.nonIdealStates.searchResultIsEmpty.title')
      : t('ticketsPage.nonIdealStates.error.title')
    const errorDescription = searchResultIsEmpty
      ? t('ticketsPage.nonIdealStates.searchResultIsEmpty.description')
      : t('ticketsPage.nonIdealStates.error.description')
    const errorActionText = searchResultIsEmpty
      ? undefined
      : t('ticketsPage.nonIdealStates.error.actionText')
    const errorAction = searchResultIsEmpty ? undefined : () => refetch()

    const totalCount = totalCapacity
      ? Math.min(totalCapacity, total_count)
      : total_count

    const availableCount = Math.max(totalCount - sold_count - pending_count, 0)

    const embedCode = `<div style="padding:56.25% 0 0 0;position:relative;"><iframe src="https://player.vimeo.com/video/948500946?h=aab3481179&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479" frameborder="0" allow="autoplay; fullscreen; picture-in-picture; clipboard-write" style="position:absolute;top:0;left:0;width:100%;height:100%;" title="Tutorial Ingressos"></iframe></div><script src="https://player.vimeo.com/api/player.js"></script>`

    return (
      <DndProvider backend={HTML5Backend}>
        <Redirect.IfNotLogged>
          <Head title={t('global.page.tickets')} />

          <ConfirmDialog
            isOpen={isRemoveTicketDialogOpen}
            title={t('ticketsPage.removeTicket.expire')}
            message={t('ticketsPage.removeTicket.body')}
            confirmText={t('ticketsPage.removeTicket.confirm')}
            cancelText={t('ticketsPage.removeTicket.cancel')}
            onConfirm={this.onConfirmRemoveDialog}
            onCancel={this.toogleRemoveDialog}
            onClose={this.toogleRemoveDialog}
          />

          <TicketCtaDrawer
            eventId={eventId}
            isOpen={isTicketCtaDrawerOpen}
            toggleDrawer={this.setOpenCtaDrawer}
            tickets
          />

          <NewTicketDialog
            eventId={eventId}
            isOpen={isNewTicketDialogOpen}
            setOpen={this.setOpenNewTicketDialog}
            onSelectType={(type) => this.setOpenFormDrawer(true, null, type)}
          />

          <FormsDrawer
            isOpen={isFormsDrawerOpen}
            ticketId={ticketId}
            toggleDrawer={this.setOpenFormsDrawer}
          />

          <TicketReorder
            isOpen={isOpenTicketReorder}
            toggleDrawer={this.setOpenTicketReorder}
            eventId={eventId}
          />

          <CategoryDrawer
            isOpen={isCategoryDrawerOpen}
            categoryToEdit={categoryToEdit}
            toggleDrawer={this.setOpenCategoryDrawer}
            eventId={trueID}
            refetch={refetch}
          />

          <ConfigBatchDisplay
            isOpen={isOpenConfigBatchDisplay}
            toggleDrawer={this.setOpenConfigBatchDisplay}
            eventId={eventId}
          />

          <ConfigTicketCustomMessage
            isOpen={isOpenTicketCustomMessage}
            toggleDrawer={this.setOpenTicketCustomMessage}
            eventId={eventId}
          />

          <ConfigEventCapacity
            isOpen={isEventCapacityDrawerOpen}
            toggleDrawer={this.setOpenEventCapacityDrawer}
            eventId={eventId}
            totalCapacity={totalCapacity}
            refetch={refetch}
          />

          <ConfigTicketPackage
            isOpen={isTicketPackageDrawerOpen}
            toggleDrawer={this.setOpenTicketPackageDrawer}
            eventId={trueID}
            tickets={tickets}
            refetch={refetch}
            packageToEdit={packageToEdit}
          />

          <CreateTicketDrawer
            isOpen={isCreateTicketDrawerOpen}
            toggleDrawer={this.setOpenFormDrawer}
            batchType={batchType}
            eventId={eventId}
            ticketId={ticketId}
            eventStartsAt={event?.starts_at}
            eventEndsAt={event?.ends_at}
            utcOffset={utcOffset}
            categoryList={categoryList}
          />

          <EventManager activeMenuItem={EventManager.TICKETS}>
            <EventManagerHeader hideActions />

            <EventHeader title={t('global.page.tickets')}>
              {!isMobile && (
                <>
                  <VideoTutorialButton embedCode={embedCode} />
                  <Spacing horizontal size={24} />
                </>
              )}
              <CreateTicketButton
                onClick={() => this.setOpenFormDrawer(true, null, null)}
                dropdownOptions={[
                  {
                    label: t('ticketsPage.createDropdownOptions.ticket'),
                    onClick: () => this.setOpenFormDrawer(true, null, null),
                  },
                  {
                    label: t('ticketsPage.createDropdownOptions.category'),
                    onClick: () => this.setOpenCategoryDrawer(true, null),
                  },
                  {
                    label: t('ticketsPage.createDropdownOptions.package'),
                    onClick: () => this.setOpenTicketPackageDrawer(true, null),
                  },
                ]}
              />
            </EventHeader>

            <ContentContainer>
              <HeaderToolbar
                embedCode={embedCode}
                setSearchQuery={setSearchQuery}
                searchQuery={searchQuery}
                pending_count={pending_count}
                sold_count={sold_count}
                total_count={totalCount}
                setOpenCtaDrawer={this.setOpenCtaDrawer}
                setOpenTicketCustomMessage={this.setOpenTicketCustomMessage}
                setOpenConfigBatchDisplay={this.setOpenConfigBatchDisplay}
                setOpenTicketReorder={this.setOpenTicketReorder}
                setOpenEventCapacity={this.setOpenEventCapacityDrawer}
                tickets={tickets}
              />

              <NonIdealStates
                loading={debouncedLoading}
                error={error || searchResultIsEmpty}
                empty={empty}
                errorTitle={errorTitle}
                errorDescription={errorDescription}
                errorActionText={errorActionText}
                onErrorButtonClick={errorAction}
                emptyTitle={t('ticketsPage.nonIdealStates.empty.title')}
                emptyActionText={t(
                  'ticketsPage.nonIdealStates.empty.actionText'
                )}
                onEmptyButtonClick={() =>
                  this.setOpenFormDrawer(true, null, null)
                }
              />

              {!debouncedLoading &&
                !searchQuery &&
                !isEmpty(ticketPackageList) && (
                  <TicketDataWrapper title={'Combos de ingressos'}>
                    {ticketPackageList.map((ticketPkg, i) => {
                      return (
                        <PackageInfo
                          eventAvailableQty={availableCount}
                          ticketList={tickets}
                          key={ticketPkg.id}
                          ticketPackage={ticketPkg}
                          isLast={i === ticketPackageList.length - 1}
                          index={i}
                          onEditTicket={() =>
                            this.setOpenTicketPackageDrawer(true, ticketPkg)
                          }
                          onPublishUnpublish={() =>
                            this.onPublishUnpublishPackage(ticketPkg)
                          }
                          onRemoveTicket={() =>
                            this.setState({
                              isRemoveTicketDialogOpen: true,
                              ticketToBeRemove: {
                                id: ticketPkg.id,
                                type: 'PACKAGE',
                              },
                            })
                          }
                        />
                      )
                    })}
                  </TicketDataWrapper>
                )}

              <TicketsWrapper
                debouncedLoading={debouncedLoading}
                error={error}
                searchResultIsEmpty={searchResultIsEmpty}
                tickets={tickets}
                setOpenFormDrawer={this.setOpenFormDrawer}
                eventId={eventId}
                onRemoveTicket={this.onRemoveTicket}
                onPublishUnpublish={this.onPublishUnpublish}
                onExportParticipants={this.onExportParticipants}
                onDuplicateTicket={this.onDuplicateTicket}
                refetch={refetch}
                loading={loading}
                setOpenCategoryDrawer={this.setOpenCategoryDrawer}
                categories={categoryList}
              />

              {!debouncedLoading &&
                !searchQuery &&
                !isEmpty(donationCampaigns) && (
                  <React.Fragment>
                    <DataGrid title="Campanhas de doação">
                      {donationCampaigns.map((donationCampaign) => (
                        <DonationCampaignDataGrid
                          key={donationCampaign.id}
                          donationCampaign={donationCampaign}
                          eventId={eventId}
                          onEditDonationCampaign={() => {
                            this.setOpenFormDrawer(
                              true,
                              donationCampaign.id,
                              'donation'
                            )
                          }}
                        />
                      ))}
                    </DataGrid>
                    <br />
                  </React.Fragment>
                )}
            </ContentContainer>
          </EventManager>
        </Redirect.IfNotLogged>
      </DndProvider>
    )
  }
}

export default flowRight(
  ScreenDetector,
  graphql(ticketOrderMutation, {
    name: 'ticketOrder',
  }),
  graphql(ticketRemoveMutation, {
    name: 'ticketRemoveMutation',
    options: {
      refetchQueries: ['eventTicketsQuery'],
    },
  }),
  graphql(updateTicketMutation, {
    name: 'updateTicketMutation',
    options: {
      refetchQueries: ['eventTicketsQuery'],
    },
  }),
  graphql(duplicateTicketMutation, {
    name: 'duplicateTicketMutation',
    options: {
      refetchQueries: ['eventTicketsQuery'],
    },
  }),
  graphql(updatePackageMutation, {
    name: 'updatePackageMutation',
    options: {
      refetchQueries: ['eventTicketsQuery'],
    },
  }),
  graphql(exportParticipantsMutation, { name: 'exportParticipantsMutation' }),
  withTranslations,
  withRouter,
  withState(
    'searchQuery',
    'setSearchQuery',
    (props) => props.router.query.search || ''
  ),
  paginatedGraphql(
    eventTicketsQuery,
    {
      options: (props) => ({
        variables: {
          eventId: props.router.query.eventId,
          filter: {
            search_query: props.searchQuery,
          },
        },
        fetchPolicy: 'cache-and-network',
      }),
    },
    'event.tickets'
  ),
  withDebouncedLoading()
)(TicketsPage)
