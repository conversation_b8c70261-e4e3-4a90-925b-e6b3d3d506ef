import { withTranslations } from 'lets-i18n'
import React, { useEffect, useMemo, useState } from 'react'
import TicketDataWrapper from '../components/TicketDataWrapper'
import CategoryInfo from '../components/TicketInfo/CategoryInfo'
import { useMutation } from '@apollo/client'
import { deleteTicketCategoryMutation } from './queries'
import PromiseNotification from 'src/components/PromiseNotification'
import withCategoryDND from './withCategoryDND'

const CategoryWrapper = ({
  categories,
  categoryTicketList,
  t,
  openEditDrawer,
  handleCategoryHover,
  eventId,
  setOpenTicketFormDrawer,
  onRemoveTicket,
  onPublishUnpublish,
  onExportParticipants,
  handleTicketHover,
  handleTicketDragEnd,
  handleTicketDragStart,
  loading,
  refetch,
  categoryDragStart,
  categoryDragHover,
  categoryDragEnd,
  categoryList,
}) => {
  const [deleteCategoryMutation] = useMutation(deleteTicketCategoryMutation)

  const handleDeleteCategory = async (id) => {
    const input = {
      id,
      node_type: 'TICKET_CATEGORY',
    }

    try {
      await PromiseNotification(
        deleteCategoryMutation({
          variables: {
            input,
          },
        }),
        t('ticketsPage.category.infoCard.removeLoading'),
        t('ticketsPage.category.infoCard.removeSuccess')
      )

      await refetch()
    } catch (err) {}
  }

  if (!categories || categories.length == 0) return null

  return (
    <TicketDataWrapper title={t('ticketsPage.category.title')}>
      {categoryList.map((category, index) => (
        <CategoryInfo
          key={category.id}
          category={category}
          ticketList={categoryTicketList[category.id] ?? []}
          index={index}
          isLast={index === categoryList.length - 1}
          openEditDrawer={openEditDrawer}
          handleCategoryHover={handleCategoryHover}
          eventId={eventId}
          loading={loading}
          setOpenTicketFormDrawer={setOpenTicketFormDrawer}
          onRemoveTicket={onRemoveTicket}
          onPublishUnpublish={onPublishUnpublish}
          onExportParticipants={onExportParticipants}
          handleTicketHover={handleTicketHover}
          handleTicketDragEnd={handleTicketDragEnd}
          handleTicketDragStart={handleTicketDragStart}
          handleDeleteCategory={handleDeleteCategory}
          categoryDragStart={categoryDragStart}
          categoryDragHover={categoryDragHover}
          categoryDragEnd={categoryDragEnd}
        />
      ))}
    </TicketDataWrapper>
  )
}

export default withCategoryDND(withTranslations(CategoryWrapper))
