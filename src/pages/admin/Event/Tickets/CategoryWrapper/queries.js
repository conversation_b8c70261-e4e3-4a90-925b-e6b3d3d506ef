import { gql } from '@apollo/client'

export const deleteTicketCategoryMutation = gql`
  mutation deleteTicketCategoy($input: InputWithNodeTypeInput!) {
    delete_node(input: $input) {
      ... on ValidationErrors {
        errors
      }
    }
  }
`

export const ReorderCategoryMutation = gql`
  mutation reorderCategories($input: ReorderTicketCategoryInput!) {
    event_categories_reorder(input: $input) {
      ... on ValidationErrors {
        errors
      }
    }
  }
`
