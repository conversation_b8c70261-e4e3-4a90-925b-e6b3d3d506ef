import React, { Component } from 'react'
import { graphql } from '@apollo/client/react/hoc'
import { ReorderCategoryMutation } from './queries'
import PromiseNotification from 'src/components/PromiseNotification'

function withCategoryDND(WrappedComponent) {
  return graphql(ReorderCategoryMutation, {
    name: 'reorderCategoryMutation',
  })(
    class extends Component {
      constructor(props) {
        super(props)

        this.state = {
          categoryList: this.updateCategoryList(),
        }
      }

      isDragging = false
      startItem
      endItem
      changeCategoryOrder

      componentDidUpdate(prevProps) {
        const { categories: prevCategories } = prevProps
        const { categories } = this.props

        if (categories !== prevCategories) {
          this.setState({
            categoryList: this.updateCategoryList(),
          })
        }
      }

      updateCategoryList() {
        const { categories } = this.props
        return [...categories]
      }

      categoryDragStart = (dragid) => {
        if (this.isDragging) return

        this.endItem = this.startItem = this.getCategoryByID(dragid)
        this.isDragging = true
        this.changeCategoryOrder = false
      }

      categoryDragHover = (dropid, dragid) => {
        const { index: dropIndex, category: dropCategory } =
          this.getCategoryByID(dropid)

        const { index: dragIndex, category: dragCategory } =
          this.getCategoryByID(dragid)

        if (dropCategory.id === dragCategory.id) return

        this.endItem = { category: dragCategory, index: dropIndex }

        const newList = [...this.state.categoryList]

        const temp = newList[dropIndex]
        newList[dropIndex] = newList[dragIndex]
        newList[dragIndex] = temp

        this.setState({
          categoryList: newList,
        })
      }

      categoryDragEnd = () => {
        this.isDragging = false

        const changedIndex = this.startItem.index !== this.endItem.index

        const { refetch } = this.props

        if (changedIndex)
          this.requestReorderCategories().finally(() => {
            setTimeout(() => refetch(), 100)
          })
      }

      requestReorderCategories = async () => {
        const { eventId, reorderCategoryMutation } = this.props
        const { categoryList } = this.state

        const input = {
          event_id: eventId,
          category_ids: categoryList.map((c) => c.id),
        }

        try {
          await PromiseNotification(
            reorderCategoryMutation({
              variables: {
                input,
              },
            }),
            'Atualizando categorias...',
            'Categorias atualizadas com sucesso!'
          )
        } catch (err) {
          console.error(err)
        }
      }

      getCategoryByID = (id) => {
        const list = this.state.categoryList
        const index = list.findIndex((c) => c.id === id)
        return { index, category: list[index] }
      }

      render() {
        return (
          <WrappedComponent
            categoryDragStart={this.categoryDragStart}
            categoryDragHover={this.categoryDragHover}
            categoryDragEnd={this.categoryDragEnd}
            {...this.state}
            {...this.props}
          />
        )
      }
    }
  )
}

export default withCategoryDND
