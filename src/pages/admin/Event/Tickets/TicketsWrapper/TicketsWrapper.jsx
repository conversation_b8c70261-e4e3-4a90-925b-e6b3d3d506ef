import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { TICKET_TYPE } from '../NewTicketDialog'
import TicketDataWrapper from '../components/TicketDataWrapper'
import TicketInfo from '../components/TicketInfo'
import { useMutation } from '@apollo/client'
import { ticketOrderMutation } from '../TicketReorder/queries'
import { withTranslations } from 'lets-i18n'
import PromiseNotification from 'src/components/PromiseNotification'
import { mutationEditTicket } from '../TicketFormDrawer/queries'
import Dialog from 'src/pages/participant/MyTickets/MyTicketsCard/RefundDialog/Dialog'
import DialogStatus from 'src/pages/participant/MyTickets/MyTicketsCard/RefundDialog/DialogStatus'
import Spacing from 'src/components/Spacing'
import './main.scss'
import withTicketDnD from './withTicketDnD'
import CategoryWrapper from '../CategoryWrapper'

const TicketsWrapper = ({
  t,
  debouncedLoading,
  error,
  searchResultIsEmpty,
  tickets,
  setOpenFormDrawer,
  eventId,
  onRemoveTicket,
  onPublishUnpublish,
  onExportParticipants,
  onDuplicateTicket,
  refetch,
  loading: loadingTickets,
  handleTicketHover,
  handleTicketDragEnd,
  handleTicketDragStart,
  handleCategoryHover,
  publicList,
  privateList,
  isDNDLoading,
  categoryTicketList,
  categories,
  setOpenCategoryDrawer,
}) => {
  const ticketListMap = [
    {
      title: t('ticketsPage.ticketWrapper.title.public'),
      list: publicList,
    },
    {
      title: t('ticketsPage.ticketWrapper.title.private'),
      list: privateList,
    },
  ]

  const isLoading = debouncedLoading || loadingTickets || isDNDLoading

  return (
    <>
      {!debouncedLoading && !error && !searchResultIsEmpty && (
        <>
          <CategoryWrapper
            categories={categories}
            categoryTicketList={categoryTicketList}
            openEditDrawer={setOpenCategoryDrawer}
            handleCategoryHover={handleCategoryHover}
            eventId={eventId}
            loading={isLoading}
            setOpenTicketFormDrawer={setOpenFormDrawer}
            onRemoveTicket={onRemoveTicket}
            onPublishUnpublish={onPublishUnpublish}
            onExportParticipants={onExportParticipants}
            handleTicketHover={handleTicketHover}
            handleTicketDragEnd={handleTicketDragEnd}
            handleTicketDragStart={handleTicketDragStart}
            refetch={refetch}
          />
          {ticketListMap.map(({ title, list }, i) => (
            <React.Fragment key={i}>
              {list.length ? (
                <TicketDataWrapper title={title}>
                  {list.map((ticket, index) => (
                    <TicketInfo
                      loading={isLoading}
                      isLast={index === list.length - 1}
                      index={index}
                      key={ticket.id}
                      ticket={ticket}
                      eventId={eventId}
                      onEditTicket={() => {
                        const batches = ticket?.batches?.nodes ?? []
                        let type = TICKET_TYPE.SINGLE
                        if (batches[0] && batches[0].price < 0.01) {
                          type = TICKET_TYPE.FREE
                        }
                        if (batches.length >= 2) {
                          type = TICKET_TYPE.BATCH
                        }

                        setOpenFormDrawer(true, ticket.id, type)
                      }}
                      onRemoveTicket={() => onRemoveTicket(ticket.id)}
                      onPublishUnpublish={() => onPublishUnpublish(ticket)}
                      onExportParticipants={() => onExportParticipants(ticket)}
                      onDuplicateTicket={() => onDuplicateTicket(ticket)}
                      handleTicketHover={handleTicketHover}
                      handleTicketDragEnd={handleTicketDragEnd}
                      handleTicketDragStart={handleTicketDragStart}
                    />
                  ))}
                </TicketDataWrapper>
              ) : null}
            </React.Fragment>
          ))}
        </>
      )}
    </>
  )
}

export default withTicketDnD(withTranslations(TicketsWrapper))
