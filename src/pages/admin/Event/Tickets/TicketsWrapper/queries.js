const { gql } = require('@apollo/client')

export const JoinCategoryTicketMutation = gql`
  mutation JoinCategoryTicket($input: JoinCategoryTicketInput!) {
    join_category_ticket(input: $input) {
      ... on ValidationErrors {
        errors
      }
    }
  }
`

export const ReorderTicketInsideCategoryMutation = gql`
  mutation reorderTicketInsideCategory(
    $input: TicketReorderTicketCategoryInput!
  ) {
    event_tickets_category_reorder(input: $input) {
      ... on ValidationErrors {
        errors
      }
    }
  }
`
