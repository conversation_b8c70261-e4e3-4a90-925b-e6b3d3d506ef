import { Component } from 'react'
import Spacing from 'src/components/Spacing'
import Dialog from 'src/pages/participant/MyTickets/MyTicketsCard/RefundDialog/Dialog'
import DialogStatus from 'src/pages/participant/MyTickets/MyTicketsCard/RefundDialog/DialogStatus'
import './main.scss'
import { withTranslations } from 'lets-i18n'
import { ticketOrderMutation } from '../TicketReorder/queries'
import { graphql } from '@apollo/client/react/hoc'
import PromiseNotification from 'src/components/PromiseNotification'
import { mutationEditTicket } from '../TicketFormDrawer/queries'
import {
  JoinCategoryTicketMutation,
  ReorderTicketInsideCategoryMutation,
} from './queries'

function withTicketDnD(WrappedComponent) {
  return graphql(JoinCategoryTicketMutation, {
    name: 'JoinCategoryTicketMutation',
  })(
    graphql(mutationEditTicket, {
      name: 'editTicketMutation',
    })(
      graphql(ReorderTicketInsideCategoryMutation, {
        name: 'reorderTicketInsideCategoryMutation',
      })(
        graphql(ticketOrderMutation, {
          name: 'ticketOrderMutation',
        })(
          withTranslations(
            class extends Component {
              constructor(props) {
                super(props)

                const { publicList, privateList, categoryTicketList } =
                  this.updateListWithProps()

                this.state = {
                  publicList,
                  privateList,
                  categoryTicketList,
                  isConfirmationOpen: false,
                  isDNDLoading: false,
                }
              }

              isDragging = false
              startItem
              endItem
              startSnaphshot
              changeCategory
              changePublicListOrder
              changeVisibility
              changeOrderInsideCategory

              componentDidUpdate(prevProps) {
                const { tickets: prevTickets, categories: prevCategories } =
                  prevProps
                const { tickets, categories } = this.props

                if (prevTickets !== tickets || categories !== prevCategories) {
                  const { publicList, privateList, categoryTicketList } =
                    this.updateListWithProps()

                  this.setState({
                    publicList,
                    privateList,
                    categoryTicketList,
                  })
                }
              }

              updateListWithProps = () => {
                const ticketList = this.props.tickets ?? []
                const publicList = []
                const privateList = []

                ticketList.forEach((t) => {
                  if (t.category?.id) return
                  var list = t.public ? publicList : privateList
                  list.push(t)
                })

                const categoryTicketList = this.props.categories.reduce(
                  (prev, category) => {
                    return {
                      ...prev,
                      [category.id]: [...category.tickets.nodes],
                    }
                  },
                  {}
                )

                return {
                  publicList,
                  privateList,
                  categoryTicketList,
                }
              }

              handleCategoryHover = (categoryid, dragid) => {
                const { index, ticket } = this.getTicketByID(dragid)

                if (ticket.category?.id === categoryid) return

                this.removeTicketFromList(index, ticket)
                this.addTicketToList(
                  ticket,
                  0,
                  this.startItem.public,
                  categoryid
                )
              }

              removeTicketFromList = (index, ticket) => {
                if (ticket.category?.id) {
                  const id = ticket.category?.id
                  const list = this.getCategoryTicketListByID(id)

                  list.splice(index, 1)

                  const { categoryTicketList } = this.state

                  this.setState({
                    categoryTicketList: {
                      ...categoryTicketList,
                      [id]: [...list],
                    },
                  })
                } else {
                  const listKey = ticket.public ? 'publicList' : 'privateList'
                  const list = [...this.state[listKey]]

                  list.splice(index, 1)

                  this.setState({
                    [listKey]: list,
                  })
                }
              }

              addTicketToList = (ticket, index, isPublic, categoryID) => {
                if (!!categoryID) {
                  const list = this.getCategoryTicketListByID(categoryID)

                  const newTicket = {
                    ...ticket,
                    category: {
                      id: categoryID,
                    },
                  }

                  this.endItem = { index, ticket: newTicket }

                  list.splice(index, 0, newTicket)

                  const { categoryTicketList } = this.state

                  this.setState({
                    categoryTicketList: {
                      ...categoryTicketList,
                      [categoryID]: [...list],
                    },
                  })
                } else {
                  const listKey = isPublic ? 'publicList' : 'privateList'
                  const list = [...this.state[listKey]]

                  const newTicket = {
                    ...ticket,
                    category: null,
                    public: isPublic,
                  }

                  list.splice(index, 0, newTicket)

                  this.endItem = { index, ticket: newTicket }

                  this.setState({
                    [listKey]: list,
                  })
                }
              }

              reorderTicketList = (ticket, startindex, endindex) => {
                this.endItem = { ticket, index: endindex }

                if (ticket.category?.id) {
                  const id = ticket.category?.id
                  const list = this.getCategoryTicketListByID(id)

                  const temp = list[startindex]
                  list[startindex] = list[endindex]
                  list[endindex] = temp

                  const { categoryTicketList } = this.state

                  this.setState({
                    categoryTicketList: {
                      ...categoryTicketList,
                      [id]: [...list],
                    },
                  })
                } else {
                  const listKey = ticket.public ? 'publicList' : 'privateList'
                  const list = [...this.state[listKey]]

                  const temp = list[startindex]
                  list[startindex] = list[endindex]
                  list[endindex] = temp

                  this.setState({
                    [listKey]: list,
                  })
                }
              }

              isTicketFromSameList = (ticket1, ticket2) => {
                if (ticket1.category?.id || ticket2.category?.id)
                  return ticket1.category?.id === ticket2.category?.id
                if (ticket1.public === ticket2.public) return true
                return false
              }

              handleTicketHover = (dropid, dragid) => {
                const { index: dropIndex, ticket: dropTicket } =
                  this.getTicketByID(dropid)

                const { index: dragIndex, ticket: dragTicket } =
                  this.getTicketByID(dragid)

                if (dropTicket.id === dragTicket.id) return

                const sameList = this.isTicketFromSameList(
                  dropTicket,
                  dragTicket
                )

                if (sameList) {
                  this.reorderTicketList(dragTicket, dragIndex, dropIndex)
                } else {
                  this.removeTicketFromList(dragIndex, dragTicket)
                  this.addTicketToList(
                    dragTicket,
                    dropIndex,
                    dropTicket.public,
                    dropTicket.category?.id
                  )
                }
              }

              getCategoryTicketListByID = (id) => {
                const { categoryTicketList } = this.state
                return categoryTicketList[id]
              }

              getTicketByID = (id) => {
                const { publicList, privateList, categoryTicketList } =
                  this.state

                const searchList = [
                  publicList,
                  privateList,
                  ...Object.values(categoryTicketList),
                ]

                for (let i = 0; i < searchList.length; i++) {
                  const list = searchList[i]
                  const index = list.findIndex((t) => t.id === id)
                  if (index < 0) continue
                  return { index, ticket: list[index] }
                }
              }

              handleTicketDragStart = (dragid) => {
                if (this.isDragging) return

                const { privateList, publicList } = this.state

                this.endItem = this.startItem = this.getTicketByID(dragid)
                this.startSnaphshot = { privateList, publicList }
                this.isDragging = true
                this.changeCategory = false
                this.changeVisibility = false
                this.changeOrderInsideCategory = false
                this.changePublicListOrder = false
              }

              revertToSnapshot = () => {
                const { publicList, privateList, categoryTicketList } =
                  this.updateListWithProps()

                this.setState({
                  publicList,
                  privateList,
                  categoryTicketList,
                })
              }

              handleTicketDragEnd = () => {
                const { categoryTicketList } = this.state

                this.isDragging = false

                const startedAsPublic = this.startItem.ticket.public

                const startedInCategory = !!this.startItem.ticket.category?.id

                const endedAsPublic = this.endItem.ticket.public

                const endedInCategory = !!this.endItem.ticket.category?.id

                const changedIndex = this.startItem.index != this.endItem.index

                const startInPublicList = startedAsPublic && !startedInCategory

                const endedInPublicList = endedAsPublic && !endedInCategory

                this.changeCategory =
                  this.startItem.ticket.category?.id !=
                  this.endItem.ticket.category?.id

                this.changePublicListOrder =
                  startInPublicList != endedInPublicList ||
                  (startInPublicList && endedInPublicList && changedIndex)

                this.changeVisibility =
                  startedAsPublic != endedAsPublic && !endedInCategory

                this.changeOrderInsideCategory =
                  this.changeCategory ||
                  (changedIndex && (endedInCategory || startedInCategory))

                const anyChange =
                  this.changeCategory ||
                  this.changePublicListOrder ||
                  this.changeVisibility

                if (
                  !anyChange &&
                  startedAsPublic != endedAsPublic &&
                  endedInCategory
                ) {
                  const { ticket } = this.startItem
                  const { index } = this.endItem

                  const categoryID = ticket.category.id

                  const newTicket = {
                    ...this.endItem.ticket,
                    public: ticket.public,
                  }

                  const newList = [...categoryTicketList[categoryID]]

                  newList[index] = newTicket

                  const newCategoryList = {
                    ...categoryTicketList,
                    [categoryID]: newList,
                  }

                  this.setState({
                    categoryTicketList: newCategoryList,
                  })
                }

                if (this.changeVisibility) {
                  this.setIsConfirmationOpen(true)
                } else {
                  this.requestChanges()
                }
              }

              requestChanges = async () => {
                const { refetch } = this.props

                if (this.changeCategory) {
                  await this.joinCategoryTicket()
                }

                if (this.changePublicListOrder) {
                  await this.updatePublicTicketOrder()
                }

                if (this.changeOrderInsideCategory) {
                  const startCategoryID = this.startItem.ticket.category?.id
                  await this.requestCategoryTicketOrder(startCategoryID)
                  const endCategoryID = this.endItem.ticket.category?.id
                  if (startCategoryID !== endCategoryID) {
                    await this.requestCategoryTicketOrder(endCategoryID)
                  }
                }

                if (
                  this.changeCategory ||
                  this.changePublicListOrder ||
                  this.changeVisibility ||
                  this.changeOrderInsideCategory
                ) {
                  setTimeout(() => refetch(), 50)
                }
              }

              requestCategoryTicketOrder = async (categoryID) => {
                if (!categoryID) return

                const { reorderTicketInsideCategoryMutation, eventId } =
                  this.props

                const list = this.getCategoryTicketListByID(categoryID)

                if (list.length === 0) return

                const input = {
                  event_id: eventId,
                  ticket_ids: list.map((t) => t.id),
                }

                try {
                  await PromiseNotification(
                    reorderTicketInsideCategoryMutation({
                      variables: {
                        input,
                      },
                    }),
                    'Atualizando categoria...',
                    'Categoria atualizada com sucesso!'
                  )
                } catch (err) {
                  console.error(err)
                }
              }

              joinCategoryTicket = async () => {
                const { ticket } = this.endItem
                const { JoinCategoryTicketMutation, t } = this.props

                const input = {
                  id: ticket.id,
                  category_id: ticket.category?.id,
                }

                this.setIsLoading(true)

                await PromiseNotification(
                  JoinCategoryTicketMutation({ variables: { input } }),
                  t(`ticketMutation.update.loading`),
                  t(`ticketMutation.update.succeed`),
                  t(`ticketMutation.update.failed`)
                )

                this.setIsLoading(false)
              }

              updatePublicTicketOrder = async () => {
                const { eventId, t, ticketOrderMutation } = this.props
                const { publicList } = this.state

                const input = {
                  event_id: eventId,
                  ticket_ids: publicList.map((t) => t.id),
                }

                if (input.ticket_ids.length == 0) return

                this.setIsLoading(true)

                await PromiseNotification(
                  ticketOrderMutation({ variables: { input } }),
                  t('ticketsPage.ticketReorder.update.loading'),
                  t('ticketsPage.ticketReorder.update.success'),
                  t('ticketsPage.ticketReorder.update.failed')
                )

                this.setIsLoading(false)
              }

              editTicketVisibility = async () => {
                const { editTicketMutation, t } = this.props
                const { ticket } = this.startItem

                const input = {
                  id: ticket.id,
                  name: ticket.name,
                  description: ticket.description,
                  public: !ticket.public,
                  available_until: ticket.available_until,
                  published: ticket.published,
                  total_available: ticket.total_available,
                  available_from: ticket.available_from,
                  buyer_pays_fee: ticket.buyer_pays_fee,
                  max_purchase_items: ticket.max_purchase_items,
                  min_purchase_items: ticket.min_purchase_items,
                  batches: ticket.batches.nodes.map((b) => ({
                    id: b.id,
                    total_available: b.total_available,
                    available_until: b.available_until,
                    price: b.price,
                  })),
                }

                try {
                  this.setIsLoading(true)

                  await PromiseNotification(
                    editTicketMutation({ variables: { input } }),
                    t(`ticketMutation.update.loading`),
                    t(`ticketMutation.update.succeed`),
                    t(`ticketMutation.update.failed`)
                  )
                } catch {
                  this.revertToSnapshot()
                  this.setIsConfirmationOpen(false)
                  this.setIsLoading(false)
                  return
                }

                await this.requestChanges()

                this.setIsConfirmationOpen(false)
                this.setIsLoading(false)
              }

              setIsConfirmationOpen = (state) => {
                this.setState({
                  isConfirmationOpen: state,
                })
              }

              setIsLoading = (state) => {
                this.setState({
                  isDNDLoading: state,
                })
              }

              handleCancelEdit = () => {
                this.revertToSnapshot()
                this.setIsConfirmationOpen(false)
              }

              handleConfirmEdit = () => {
                this.editTicketVisibility()
              }

              render() {
                const { t } = this.props
                const { isConfirmationOpen, isDNDLoading } = this.state

                return (
                  <>
                    <Dialog
                      isOpen={isConfirmationOpen}
                      onClose={isDNDLoading ? null : this.handleCancelEdit}
                      actions={
                        isDNDLoading || !isConfirmationOpen
                          ? null
                          : [
                              {
                                name: t(
                                  'ticketsPage.ticketWrapper.confirmation.cancel'
                                ),
                                onClick: this.handleCancelEdit,
                              },
                              {
                                name: t(
                                  'ticketsPage.ticketWrapper.confirmation.cta'
                                ),
                                type: 'primary',
                                onClick: this.handleConfirmEdit,
                              },
                            ]
                      }
                    >
                      <DialogStatus
                        status={
                          isDNDLoading || !isConfirmationOpen
                            ? 'loading'
                            : 'warning'
                        }
                      />
                      <h3 className="ticket-confirmation-dialog__title">
                        {t('ticketsPage.ticketWrapper.confirmation.title')}
                      </h3>
                      <Spacing size={16} />
                      <p className="ticket-confirmation-dialog__text">
                        {isDNDLoading || !isConfirmationOpen
                          ? t(`ticketMutation.update.loading`)
                          : t(
                              `ticketsPage.ticketWrapper.confirmation.text.${
                                this.endItem.ticket.public
                                  ? 'toPublic'
                                  : 'toPrivate'
                              }`
                            )}
                      </p>
                    </Dialog>
                    <WrappedComponent
                      handleTicketDragStart={this.handleTicketDragStart}
                      handleTicketHover={this.handleTicketHover}
                      handleTicketDragEnd={this.handleTicketDragEnd}
                      handleCategoryHover={this.handleCategoryHover}
                      {...this.state}
                      {...this.props}
                    />
                  </>
                )
              }
            }
          )
        )
      )
    )
  )
}

export default withTicketDnD
