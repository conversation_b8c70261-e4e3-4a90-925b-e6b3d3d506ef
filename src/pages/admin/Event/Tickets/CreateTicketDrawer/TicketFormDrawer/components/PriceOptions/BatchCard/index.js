import React, { Fragment } from 'react'
import moment from 'moment'
import 'moment/locale/pt'
import { withTranslations } from 'lets-i18n'
import { numericWhiteList } from 'src/utils/strings'
import { Card, Icon } from '@blueprintjs/core'
import { InfoIconTip } from 'src/components/Tooltips'
import InlineDateTimeField from 'src/components/FormUtils/InlineDateTimeField'
import InputField from 'src/components/FormUtils/InputField'
import { composeValidators } from 'src/components/FormUtils/validators'
import { TicketPrice } from '../../TicketPrice'
import { isCommitted } from '../../../functions'
import {
  validateBatchesTurn,
  validateBatchQuantityBiggerThanCommitted,
  validateBatchQuantitySmallerThanTicketQuantity,
  validateBatchDateAfterSalesStart,
  validateBatchDateBeforeSalesEnd,
  validateBatchDateAfterPreviousBatch,
} from '../../../functions/validate'
import Spacing from 'src/components/Spacing'

const BatchCard = withTranslations(props => {
  const {
    event,
    values,
    index,
    isFinal,
    isFirst,
    isPrecedingCommittedBatch,
    onChangeBatch,
    onClickRemoveBatch,
    t,
    name,
  } = props
  const { fee, batches } = values

  const batch = batches[index]

  if (!batch) return null

  const isAlreadyCommitted = isCommitted(batch)

  return (
    <Card className="batch-card">
      <div className="batch-card__header">
        <h6 className="batch-card__title">
          {index + 1}º{t('ticketMutation.batch')}
          {isFinal && ` (${t('ticketMutation.finalBatch')})`}
        </h6>
        {onClickRemoveBatch &&
          !isAlreadyCommitted &&
          !isPrecedingCommittedBatch && (
            <Icon
              className="batch-card__button-icon"
              icon="trash"
              onClick={onClickRemoveBatch}
            />
          )}
      </div>
      <TicketPrice
        disabled={isAlreadyCommitted || isPrecedingCommittedBatch}
        className="batch-card__row"
        hideFreeCheck={!isFirst}
        batch={batch}
        event={event}
        fee={fee}
        name={name}
        onChange={(name, value) => onChangeBatch(index, name, value)}
        isFirst={isFirst}
      />
      <Spacing size={20} />
      <div className="batch-card__divider" />
      <BottomContent {...props} />
    </Card>
  )
})

const BottomContent = withTranslations(
  ({
    t,
    language,
    isFinal,
    isPrecedingCommittedBatch,
    values,
    index,
    name,
    event,
  }) => {
    const { quantity, batches } = values
    const batch = batches[index]
    const { committedAmount } = batch

    const focusInput = cssQuery => {
      document.querySelectorAll(cssQuery)[0].focus()
    }

    if (isFinal) {
      const { end } = values

      return (
        <div className="batch-card__callout">
          <h6>{t('ticketMutation.finalBatchCallout.title')}</h6>
          {t('ticketMutation.finalBatchCallout.beginning')}
          <a
            onClick={() => {
              focusInput('#endDate input')
            }}
          >
            {end &&
              moment(end)
                .locale(language)
                .format('l LT')}
            {!end && t('ticketMutation.finalBatchCallout.addDate')}{' '}
          </a>
          {t('ticketMutation.finalBatchCallout.middle')}
          <a
            onClick={() => {
              focusInput('input#quantity')
            }}
          >
            {quantity ? `${quantity} ` : ''}{' '}
          </a>
          {t('ticketMutation.finalBatchCallout.end')}
        </div>
      )
    }

    const quantityHelperText =
      committedAmount > 0
        ? `${t('ticketMutation.minimum')}: ${committedAmount}`
        : null

    const endsAtValidate = composeValidators(
      validateBatchesTurn(
        t('ticketMutation.errors.batchesTurn'),
        batch,
        isFinal
      ),
      validateBatchDateAfterSalesStart(
        t('ticketMutation.errors.batchesDateThanStart')
      ),
      validateBatchDateBeforeSalesEnd(
        t('ticketMutation.errors.batchesDateThanEnd')
      ),
      validateBatchDateAfterPreviousBatch(
        t('ticketMutation.errors.batchesDateThanOlders'),
        index
      )
    )

    const amountValidate = composeValidators(
      validateBatchesTurn(
        t('ticketMutation.errors.batchesTurn'),
        batch,
        isFinal
      ),
      validateBatchQuantitySmallerThanTicketQuantity(
        t('ticketMutation.errors.batchesQuantityBiggerThanTicketQuantity'),
        batch
      ),
      validateBatchQuantityBiggerThanCommitted(
        t('ticketMutation.errors.batchesQuantityMinimum'),
        batch
      )
    )

    return (
      <Fragment>
        <div className="batch-card__title">
          <h6>{t('ticketMutation.batchTurn')}</h6>
          <InfoIconTip content={t('ticketMutation.whatHappensFirst')} />
        </div>
        <div className="batch-card__turn-row">
          <InlineDateTimeField
            label={t('ticketMutation.specificDate')}
            // disabled={isPrecedingCommittedBatch}
            name={`${name}.endsAt`}
            validate={endsAtValidate}
          />

          <div className="batch-card__turn-row-and-or">
            <span className="batch-card__turn-row-and-or-text">
              {t('ticketMutation.andOr')}
            </span>
          </div>
          <div className="batch-card__turn-quantity">
            <InputField
              label={t('ticketMutation.saleQuantity')}
              helperText={quantityHelperText}
              className="batch-card__turn-quantity-form-group"
              whitelist={numericWhiteList()}
              placeholder="100"
              // disabled={isPrecedingCommittedBatch}
              name={`${name}.amount`}
              validate={amountValidate}
            />

            <div className="batch-card__turn-quantity-label">
              {t('ticketMutation.tickets')}
            </div>
          </div>
        </div>
      </Fragment>
    )
  }
)

export default BatchCard
