import moment from 'moment'
import 'moment/locale/pt'
import {
  dateToStringOnGivenUtcOffset,
  dateFromStringIgnoringOffset,
} from 'src/utils/dateTime'
import { newBatch } from './batch'

export const applySuggestedEnd = (form, event, language) => {
  if (event && event.ends_at) {
    const eventStartDateMinus2Hours = moment(
      dateFromStringIgnoringOffset(event.ends_at)
    )
      .subtract(2, 'hour')
      .locale(language)
      .toDate()

    if (eventStartDateMinus2Hours < moment().toDate()) return

    form.end = eventStartDateMinus2Hours
  }
}

export const newForm = () => ({
  name: '',
  quantity: '',
  description: '',
  batches: [newBatch()],
  start: moment().subtract(2, 'hours').toDate(),
  end: null,
  fee: 'BUYER',
  type: 'SINGLE',
  visibility: 'PUBLIC',
  minLimit: '1',
  maxLimit: '10',
  termsOfUse: '',
})

export const mutationInputFrom = (form, event) => {
  const {
    name,
    description,
    visibility,
    quantity,
    fee,
    start,
    end,
    maxLimit,
    minLimit,
    batches,
    // termsOfUse,
  } = form

  const ticket = {
    name,
    description: description || '',
    public: visibility === 'PUBLIC',
    published: true,
    total_available: parseInt(quantity),
    buyer_pays_fee: fee === 'BUYER',
    available_until: dateToStringOnGivenUtcOffset(end, event.utc_offset),
    available_from: dateToStringOnGivenUtcOffset(start, event.utc_offset),
    min_purchase_items: parseInt(minLimit),
    // terms_of_use: termsOfUse || '',
    batches: batches.map((batch) => {
      const { price, free, endsAt, amount, id } = batch
      return {
        total_available: amount ? parseInt(amount) : null,
        available_until: dateToStringOnGivenUtcOffset(endsAt, event.utc_offset),
        price: free ? 0 : price,
        id,
      }
    }),
  }
  if (maxLimit) ticket.max_purchase_items = parseInt(maxLimit)

  return ticket
}

export const formFromTicketData = (ticketData) => {
  const batches = ticketData.batches.nodes

  return {
    name: ticketData.name,
    quantity: ticketData.total_available.toString(),
    description: ticketData.description || '',
    batches: batches.map((batch, index) => {
      const isLastBatch = index >= batches.length - 1
      const amount = batch.total_available
        ? batch.total_available.toString()
        : ''
      const endsAt = isLastBatch
        ? dateFromStringIgnoringOffset(ticketData.available_until)
        : dateFromStringIgnoringOffset(batch.available_until)
      return {
        price: batch.price,
        free: batch.price === 0,
        endsAt,
        amount,
        committedAmount: batch.committed_amount,
        id: batch.id,
      }
    }),
    start: dateFromStringIgnoringOffset(ticketData.available_from),
    end: dateFromStringIgnoringOffset(ticketData.available_until),
    fee: ticketData.buyer_pays_fee ? 'BUYER' : 'OWNER',
    type: batches.length === 1 ? 'SINGLE' : 'BATCH',
    visibility: ticketData.public ? 'PUBLIC' : 'PRIVATE',
    minLimit: ticketData.min_purchase_items.toString(),
    maxLimit: ticketData.max_purchase_items.toString(),
    committedAmount: ticketData.committed_amount,
    termsOfUse: ticketData.terms_of_use,
    category: ticketData?.category?.id,
  }
}
