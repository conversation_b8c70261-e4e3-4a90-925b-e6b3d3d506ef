import { useState, useEffect } from 'react'
import PromiseNotification from 'src/components/PromiseNotification'

export const useConfiguration = ({ eventConfig, handleSaveConfig, t }) => {
  const [configValues, setConfigValues] = useState(null)
  const [isLoadingConfig, setIsLoadingConfig] = useState(false)

  const [slug, setSlug] = useState(null)
  const [apiAuthorizationToken, setApiAuthorizationToken] = useState(null)

  const setValuesFromRequestCheckoutConfig = (checkout) => {
    if (!checkout) return
    const {
      require_invoice_info,
      notify_each_purchase,
      accepts_foreign_card,
      boleto_expiration_days_from_now,
      disable_bank_slip,
      api_authorization_token,
      event_slug,
    } = checkout.purchase
    const one_participation_per_document =
      checkout.ticket.one_participation_per_document.active

    setSlug(event_slug || null)
    setApiAuthorizationToken(api_authorization_token || null)

    setConfigValues({
      require_invoice_info,
      notify_each_purchase,
      one_participation_per_document,
      accepts_foreign_card,
      boleto_expiration_days_from_now:
        boleto_expiration_days_from_now !== null
          ? 28
          : boleto_expiration_days_from_now,
      disable_bank_slip,
      api_authorization_token,
    })
  }

  const [shouldPerformMutation, setShouldPerformMutation] = useState(false)

  const handleChangeConfigValue = ({ configKey, value }) => {
    value =
      configKey === 'boleto_expiration_days_from_now'
        ? value
          ? 28
          : null
        : value

    setConfigValues((prevValues) => ({
      ...prevValues,
      [configKey]: value,
    }))

    setShouldPerformMutation(true)
  }

  useEffect(() => {
    if (shouldPerformMutation) {
      setIsLoadingConfig(true)

      PromiseNotification(
        handleSaveConfig(configValues),
        t('configurationPage.resource_request_status.loading'),
        t('configurationPage.resource_request_status.success'),
        t('configurationPage.resource_request_status.error'),
        null,
        1500
      )
        .then((checkout) => {
          setValuesFromRequestCheckoutConfig(checkout)
        })
        .catch(() => {})
        .finally(() => {
          setIsLoadingConfig(false)
          setShouldPerformMutation(false)
        })
    }
  }, [shouldPerformMutation])

  useEffect(() => {
    if (eventConfig.loading || configValues !== null) return
    setValuesFromRequestCheckoutConfig(eventConfig?.checkout)
  }, [eventConfig.loading, configValues])

  return {
    handleChangeConfigValue,
    configValues,
    isLoadingConfig,
    slug,
    apiAuthorizationToken,
  }
}
