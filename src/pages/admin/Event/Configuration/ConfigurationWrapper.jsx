import { withTranslations } from 'lets-i18n'
import { useState } from 'react'
import ConfigurationCard from './ConfigurationCard'
import { configurationList } from './configurationList'
import './scss/main.scss'
import { useConfiguration } from './useConfiguration'

const ConfigurationWrapper = ({ handleSaveConfig, checkout, loading, t }) => {
  const [isWidgetOpen, setIsWidgetOpen] = useState(false)

  const eventConfig = {
    loading,
    checkout,
  }

  const {
    handleChangeConfigValue,
    configValues,
    isLoadingConfig,
    slug,
    apiAuthorizationToken,
  } = useConfiguration({ eventConfig, handleSaveConfig, t })

  const toggleWidget = () => setIsWidgetOpen((curr) => !curr)

  if (loading || configValues === null) return null

  return (
    <>
      <div className="configurations__card-wrapper">
        {configurationList.map((config, i) => {
          const { configKey } = config
          return (
            <ConfigurationCard
              {...config}
              index={i}
              key={i}
              handleActivation={
                configKey ? handleChangeConfigValue : toggleWidget
              }
              value={configKey ? configValues[configKey] : false}
              disabled={isLoadingConfig}
              slug={slug}
              authToken={apiAuthorizationToken}
            />
          )
        })}
      </div>
      {/* <WidgetsDrawer isOpen={isWidgetOpen} toggleDrawer={toggleWidget} /> */}
    </>
  )
}

export default withTranslations(ConfigurationWrapper)
