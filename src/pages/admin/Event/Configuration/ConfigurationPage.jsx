import { useState } from 'react'
import { useRouter } from 'next/router'
import { useQuery, useMutation } from '@apollo/client'
import { withTranslations } from 'lets-i18n'
import EventManager from 'src/components/EventManager'
import ContentContainer from 'src/components/EventManager/ContentContainer'
import EventManagerHeader from 'src/components/EventManager/EventManagerHeader'
import Head from 'src/components/Head'
import Redirect from 'src/components/Redirect'
import { eventConfigSetQuery, eventConfigSetMutation } from './queries'
import './scss/main.scss'
import ConfigurationWrapper from './ConfigurationWrapper'

const ConfigurationPage = ({ t }) => {
  const router = useRouter()
  const { eventId } = router.query
  const { data, loading } = useQuery(eventConfigSetQuery, {
    variables: { eventId },
  })

  const [eventConfigMutation] = useMutation(eventConfigSetMutation)

  const handleSaveConfig = async (configValues) => {
    const {
      require_invoice_info,
      notify_each_purchase,
      one_participation_per_document,
      accepts_foreign_card,
      boleto_expiration_days_from_now,
      disable_bank_slip,
    } = configValues

    const input = {
      event_id: eventId,
      checkout: {
        ticket: {
          one_participation_per_document: {
            active: one_participation_per_document,
            scope: 'EVENT',
          },
        },
        purchase: {
          require_invoice_info,
          notify_each_purchase,
          accepts_foreign_card,
          boleto_expiration_days_from_now,
          disable_bank_slip,
        },
      },
    }

    const r = await eventConfigMutation({ variables: { input } })

    const checkout = r?.data?.back_office_event_config_set?.config?.checkout

    return checkout
  }

  const checkout = data?.node?.config?.checkout

  return (
    <Redirect.IfNotLogged>
      <Head title={t('configurationPage.title')} />
      <EventManager activeMenuItem={EventManager.CONFIGURATIONS}>
        <EventManagerHeader sectionTitle={t('configurationPage.title')} />
        <ContentContainer>
          <ConfigurationWrapper
            handleSaveConfig={handleSaveConfig}
            checkout={checkout}
            loading={loading}
          />
        </ContentContainer>
      </EventManager>
    </Redirect.IfNotLogged>
  )
}

export default withTranslations(ConfigurationPage)
