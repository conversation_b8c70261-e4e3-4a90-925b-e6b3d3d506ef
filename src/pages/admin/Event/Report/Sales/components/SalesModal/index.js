import Modal from 'src/components/Modal'
import TableDetails from '../TableDetails'
import { currencyParser } from 'src/utils'
import dayjs from 'dayjs'
import './style.scss'

const BaseSalesModal = ({
  open,
  onClose,
  totalText,
  total,
  children,
  title,
}) => {
  return (
    <Modal open={open} onClose={() => onClose?.()} title={title}>
      <div className="report-sales-modal-content">{children}</div>
      <div className="report-sales-modal-footer">
        <p>{totalText}</p>
        <p>{total}</p>
      </div>
    </Modal>
  )
}

const SalesModal = ({ t, isOpen, onClose, event }) => {
  const totalSales = event?.financial_statement?.gross_income?.tickets || 0
  const donations = event?.financial_statement?.gross_income?.donations || 0
  const manualSales = event?.financial_statement?.gross_income?.manual || 0
  const pdv = event?.financial_statement?.gross_income?.pdv || 0

  return (
    <BaseSalesModal
      open={isOpen}
      onClose={onClose}
      title={t('salesReport.totalSales.details.title')}
      totalText={t('salesReport.totalSales.details.total')}
      total={currencyParser(totalSales + donations + manualSales)}
    >
      <TableDetails
        title={t('salesReport.totalSales.details.salesByLets')}
        items={[
          {
            text: t('salesReport.totalSales.details.ticketsSales'),
            value: currencyParser(totalSales),
          },
          {
            text: t('salesReport.totalSales.details.donations'),
            value: currencyParser(donations),
          },
          {
            text: t('salesReport.totalSales.details.subtotal'),
            value: currencyParser(totalSales + donations),
            subtotal: true,
          },
        ]}
      />
      <TableDetails
        title={t('salesReport.totalSales.details.salesByOthers')}
        items={[
          {
            text: t('salesReport.totalSales.details.manuallyAdded'),
            value: currencyParser(manualSales),
          },
          {
            text: 'PDV', // Não alterar, temos dependência no backend, caso seja necessário alterar, verificar com o backend.
            value: currencyParser(pdv),
          },
        ]}
      />
    </BaseSalesModal>
  )
}

const PaymentDetailsModal = ({ t, isOpen, onClose, event }) => {
  const paidServiceFee = event?.financial_statement?.paid_service_fee || 0
  const totalSales = event?.financial_statement?.gross_income?.tickets || 0
  const donations = event?.financial_statement?.gross_income?.donations || 0
  const amountRehearse =
    event?.financial_statement?.gross_income?.amount_rehearse || 0

  return (
    <BaseSalesModal
      open={isOpen}
      onClose={onClose}
      title={t('salesReport.transferAmount.details.title')}
      totalText={t('salesReport.transferAmount.details.total')}
      total={currencyParser(amountRehearse)}
    >
      <TableDetails
        title={t('salesReport.transferAmount.details.salesByLets')}
        items={[
          {
            text: t('salesReport.transferAmount.details.ticketsSales'),
            value: currencyParser(totalSales),
          },
          {
            text: t('salesReport.transferAmount.details.donations'),
            value: currencyParser(donations),
          },
          {
            text: t('salesReport.transferAmount.details.subtotal'),
            value: currencyParser(totalSales + donations),
            subtotal: true,
          },
        ]}
      />
      <TableDetails
        info="Mostramos aqui as taxas Lets, mesmo que ela tenha sido repassada e paga pelo cliente. Ou seja, somos transparentes e mostramos o valor das taxas, independente se foi repassada ou absorvida."
        title={t('salesReport.transferAmount.details.letsFee')}
        items={[
          {
            text: t('salesReport.transferAmount.details.onlineLetsFee'),
            value: currencyParser(paidServiceFee),
          },
        ]}
      />
    </BaseSalesModal>
  )
}

const PaymentsModal = ({ t, isOpen, onClose, event }) => {
  const amountReleased =
    event?.financial_statement?.gross_income?.amount_released || 0
  const transfer_list =
    event?.financial_statement?.gross_income?.transfer_list?.lists?.nodes || []

  return (
    <BaseSalesModal
      open={isOpen}
      onClose={onClose}
      title={t('salesReport.amountAlreadyTransferred.details.title')}
      totalText={t('salesReport.amountAlreadyTransferred.details.total')}
      total={currencyParser(amountReleased)}
    >
      <TableDetails
        title={t('salesReport.amountAlreadyTransferred.details.summary')}
        items={transfer_list.map((item) =>
          Object({
            text: dayjs(item.date).format('DD/MM/YYYY HH:mm:ss'),
            value: currencyParser(item.value),
          })
        )}
        emptyMessage={t('salesReport.amountAlreadyTransferred.details.empty')}
      />
    </BaseSalesModal>
  )
}

export { SalesModal, PaymentDetailsModal, PaymentsModal }
