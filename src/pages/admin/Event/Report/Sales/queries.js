import { gql } from '@apollo/client'
import apollo from 'config/apollo'

export const eventAnalyticsQuery = gql`
  query eventAnalyticsQuery(
    $eventId: ID!
    $filter: HistogramDateFilterInputType
  ) {
    node(id: $eventId, node_type: EVENT) {
      ... on Event {
        id
        name
        slug
        user_id
        available_advance
        net_percentage
        already_anticipated
        advance_active
        advance_tax
        advance_percentage
        advance_time
        ends_at
        transferred_value
        transfer_date
        split_transactions {
          nodes {
            id
            state
            value
            updated_at
            created_at
            receiving_date
          }
        }
        tickets {
          nodes {
            id
            name
            confirmed_amount
            total_available
            batches {
              nodes {
                id
                committed_amount
                ordering
                price
                confirmed_amount
                total_available
                available_until
              }
            }
          }
        }
        reports {
          tickets {
            available_count
            total_count
            sold_count
            pending_count
            canceled_count
            average_ticket
          }
          page_views {
            total
            percentage_conversion
          }
          histogram(filter: $filter) {
            min_date
            max_date
            max_allowed_date
            max_range_length_in_days

            tickets_sold {
              online {
                date
                value
              }
            }
            total_income {
              tickets {
                date
                value
              }
              donations {
                date
                value
              }
            }
            donations_count {
              date
              value
            }
          }
        }
        financial_statement {
          paid_service_fee
          gross_income {
            tickets
            manual
            pdv
            vendor
            donations
            pending_sales
            canceled_sales
            amount_released
            amount_rehearse
            transfer_list {
              lists {
                nodes {
                  date
                  value
                }
              }
            }
          }
        }
      }
    }
  }
`

export const invoiceInfoQuery = gql`
  query invoiceInfo($id: String!) {
    user(id: $id) {
      invoice_info {
        id
        bank_account {
          pix
        }
      }
    }
  }
`

export const antecipateMutation = gql`
  mutation money_out_event($event_id: ID!, $value: Float!) {
    money_out_event(
      input: {
        event_id: $event_id,
        value: $value
      }
    ) {
      ... on ValidationErrors {
        errors
      }
      ... on Event {
        available_advance
        net_percentage
        already_anticipated
        advance_active
        schedulings {
          nodes {
            id
            value
            updated_at
            created_at
          }
        }
        split_transactions {
          nodes {
            id
            state
            value
            updated_at
            created_at
          }
        }
      }
    }
  }
`

export const queryEventAnalytics = async (filters) => {
  const { data } = await apollo
    .getClient()
    .query({ query: eventAnalyticsQuery, variables: filters })

  return {
    ...data.node,
  }
}
