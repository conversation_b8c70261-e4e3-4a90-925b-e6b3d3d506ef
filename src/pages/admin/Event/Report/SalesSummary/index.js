import React from 'react'
import { withTranslations } from 'lets-i18n'
import { withRouter } from 'next/router'
import { graphql } from '@apollo/client/react/hoc'
import { Card } from '@blueprintjs/core'
import ContentContainer from 'src/components/EventManager/ContentContainer'
import Redirect from 'src/components/Redirect'
import Head from 'src/components/Head'
import EventManager from 'src/components/EventManager'
import EventManagerHeader from 'src/components/EventManager/EventManagerHeader'
import classNames from 'classnames'
import { get, flowRight } from 'lodash'
import moment from 'moment'
import 'moment/locale/pt'
import { NonIdealStatesBoundary } from 'src/components/NonIdealState'
import { eventSalesSummaryQuery, nfeExportMutation } from './queries'
import TableTickets from './TableTickets'
import TablePaymentMethod from './TablePaymentMethod'
import TableTotals from './TableTotals'
import './scss/main.scss'
import PromiseNotification from 'src/components/PromiseNotification'
import { fakeLinkDownload } from 'src/utils'
import TableNFE from './TableNFE'
import PackageSalesTable from './PackageSalesTable'

const SalesSummary = ({ t, language, eventSales, nfeExport, router }) => {
  const { node, loading, error } = eventSales

  const byPaymentMethod = get(
    node,
    'reports.bordereau.sales.by_payment_method',
    []
  )

  const byTickets = get(node, 'reports.bordereau.sales.by_ticket', [])
  const byPackage = get(node, 'reports.bordereau.sales.by_package', [])
  const totalFee = get(node, 'financial_statement.paid_service_fee', null)

  const totalTicketsIncome = get(
    node,
    'financial_statement.gross_income.tickets',
    0
  )

  const totalDonationsIncome = get(
    node,
    'financial_statement.gross_income.donations',
    0
  )

  const totalManualIncome = get(
    node,
    'financial_statement.gross_income.manual',
    0
  )

  const subTotal = totalTicketsIncome + totalDonationsIncome - totalFee

  const totals = get(node, 'reports.bordereau.sales.totals', [])
  const startsAt = get(node, 'eventSales.starts_at', null)

  let eventName = node ? node.name : ''
  if (startsAt)
    eventName += ` - ${moment(startsAt).locale(language).format('L')}`

  const handleNfeExport = () => {
    const eventId = router.query.eventId

    const variables = {
      input: {
        event_id: eventId,
      },
    }

    PromiseNotification(
      () => nfeExport({ variables }),
      t('Exportant relatório para NFE...'),
      t('Relatório exportado com sucesso!'),
      t('Desculpe-nos, mas não conseguimos exportar o relatório.')
    ).then((result) => {
      console.log(result)

      const url = get(
        result,
        'data.back_office_event_nfe_export.export_url',
        null
      )
      if (url) fakeLinkDownload(url)
    })
  }

  return (
    <Redirect.IfNotLogged>
      <Head title={t('global.page.eventSalesSummary')} />
      <EventManager activeMenuItem={EventManager.SALES_SUMMARY}>
        <EventManagerHeader
          sectionTitle={t('global.page.eventSalesSummary')}
          actions={
            <>
              <EventManagerHeader.Action
                icon="print"
                onClick={() => window.print()}
              >
                {t('salesSummary.actions.print')}
              </EventManagerHeader.Action>
            </>
          }
        />

        <ContentContainer>
          <NonIdealStatesBoundary
            loading={loading}
            error={error}
            empty={
              totalTicketsIncome + totalManualIncome + totalDonationsIncome ===
              0
            }
            visual="timeline-line-chart"
            errorTitle={t('salesSummary.nonIdealStates.error.title')}
            errorDescription={t(
              'salesSummary.nonIdealStates.error.description'
            )}
            errorActionText={t('salesSummary.nonIdealStates.error.actionText')}
            onErrorButtonClick={() => {
              eventSales.refetch()
            }}
            emptyTitle={t('salesSummary.nonIdealStates.empty.title')}
          >
            <Card
              elevation={Card.ELEVATION_ONE}
              className="sales-summary__card"
            >
              <div
                className={classNames(
                  'print-only',
                  'sales-summary__event-name'
                )}
              >
                {eventName}
              </div>

              <PackageSalesTable byPackage={byPackage} />
              <TableTickets byTickets={byTickets} />
              <TablePaymentMethod
                byPaymentMethod={byPaymentMethod}
                totals={totals}
              />
              <TableTotals
                totalFee={totalFee}
                totalTicketsIncome={totalTicketsIncome}
                totalDonationsIncome={totalDonationsIncome}
                subTotal={subTotal}
              />
            </Card>
          </NonIdealStatesBoundary>
        </ContentContainer>
      </EventManager>
    </Redirect.IfNotLogged>
  )
}

export default flowRight(
  withTranslations,
  withRouter,
  graphql(eventSalesSummaryQuery, {
    name: 'eventSales',
    options: (props) => ({
      variables: {
        eventId: props.router.query.eventId,
      },
    }),
  }),
  graphql(nfeExportMutation, {
    name: 'nfeExport',
  })
)(SalesSummary)
