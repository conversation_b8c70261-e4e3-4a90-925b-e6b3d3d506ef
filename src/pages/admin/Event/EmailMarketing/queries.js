import { gql } from '@apollo/client'

export const eventCampaigns = gql`
  query eventCampaigns($eventId: ID!) {
    node(id: $eventId, node_type: EVENT) {
      ... on Event {
        email_campaigns {
          nodes {
            id
            name
            email_body
            email_lists {
              nodes {
                id
              }
            }
            delivery_options
            total_participations
            created_at
          }
        }
      }
    }
  }
`;

export const eventLists = gql`
  query eventLists($eventId: ID!) {
    node(id: $eventId, node_type: EVENT) {
      ... on Event {
        email_lists {
          nodes {
            id
            name
            gender
            age_range_start
            age_range_end
            birthday_start
            birthday_end
            all_ages
            list_type
            total_participations
            created_at
          }
        }
      }
    }
  }
`;

export const userEvents = gql`
  query events($userId: String!) {
    user(id: $userId) {
      events {
        nodes {
          id
          name
          total_participations_in_lists
        }
      }
    }
  }
`;