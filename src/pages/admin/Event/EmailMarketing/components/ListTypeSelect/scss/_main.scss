@import 'src/styles/abstracts/_colors.scss';

.list-type-select {
  display: flex;
  gap: 20px;
  font-family: Work Sans;

  &__option {
    display: flex;
    flex-direction: column;
    padding: 20px;
    border: 1px solid #f4f4f4;
    border-radius: 10px;
    cursor: pointer;

    &:hover {
      box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.1);
    }

    &__icon {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #085ee5;
      color: #fff;
      border-radius: 100%;
      margin-bottom: 16px;
    }
  }

  h3 {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.2;
    color: #1e2023;
  }

  p {
    color: #34363c;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.2;
    margin: 0;
  }
}
