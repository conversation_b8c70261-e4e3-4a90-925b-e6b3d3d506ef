import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

import './scss/_main.scss';

const ListTypeSelect = ({ options }) => {
  return (
    <div className='list-type-select'>
      {options.map((option) => (
        <div 
          key={option.value}
          className='list-type-select__option'
          onClick={() => option.onSelect(option.id)}
        >
          <div className='list-type-select__option__icon'>
            <FontAwesomeIcon icon={option.icon} size='lg' />
          </div>
          <h3>{option.label}</h3>
          <p>{option.description}</p>
        </div>
      ))}
    </div>
  );
}

export default ListTypeSelect;