.list-table-row {
  font-family: 'Work Sans';
  border: 1px solid #c2c3c7;
  border-bottom: none;
  background-color: #ffffff;
  padding: 16px;
  display: flex;
  align-items: center;

  p {
    margin: 0;
  }

  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    &__title {
      display: flex;
      flex-direction: column;
      gap: 8px;

      span {
        font-size: 12px;
        font-weight: 400;
        color: #4c4f54;
      }

      p {
        font-size: 14px;
        font-weight: 400;
        color: #34363c;
      }
    }

    &__date {
      display: flex;
      flex-direction: column;
      gap: 8px;

      span {
        font-size: 12px;
        font-weight: 400;
        color: #4c4f54;
      }

      p {
        font-size: 14px;
        font-weight: 400;
        color: #34363c;
      }
    }

    &__participants {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;

      span {
        font-size: 12px;
        font-weight: 400;
        color: #4c4f54;
      }

      p {
        font-size: 12px;
        font-weight: 400;
        color: #34363c;
        background-color: #fceab0;
        padding: 4px 6px;
        border-radius: 4px;
      }
    }

    &__list {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;

      span {
        font-size: 12px;
        font-weight: 400;
        color: #4c4f54;
      }

      p {
        font-size: 12px;
        font-weight: 400;
        color: #34363c;
        background-color: #dee2f0;
        padding: 4px 6px;
        border-radius: 4px;
      }
    }

    &__status {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;

      span {
        font-size: 12px;
        font-weight: 400;
        color: #4c4f54;
      }

      p {
        font-size: 12px;
        font-weight: 400;
        color: #34363c;
        background-color: #dee2f0;
        padding: 4px 6px;
        border-radius: 4px;
      }
    }
  }

  &:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  &:last-child {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-bottom: 1px solid #c2c3c7;
  }
}
