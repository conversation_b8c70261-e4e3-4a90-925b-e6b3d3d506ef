import EventActionButton from 'src/components/EventActionButton';
import { faEllipsis } from '@fortawesome/free-solid-svg-icons';
import dayjs from 'dayjs';

import './scss/_main.scss';

const ListTableRow = ({ list }) => {
  const { name, created_at, total_participations, list_type, status } = list;

  return (
    <div className="list-table-row">
      <div className="list-table-row__content">
        <div className="list-table-row__content__title">
          <span>Nome da lista</span>
          <p>{name}</p>
        </div>
        <div className="list-table-row__content__date">
          <span>Data da criação</span>
          <p>{dayjs(created_at).format('DD/MM/YYYY HH:mm')}</p>
        </div>
        <div className="list-table-row__content__participants">
          <span>Total de participantes</span>
          <p>{total_participations}</p>
        </div>
        <div className="list-table-row__content__list">
          <span>Lista de participantes</span>
          <p>{list_type}</p>
        </div>
        <div className="list-table-row__content__status">
          <span>Status</span>
          <p>{status}</p>
        </div>
        <div className="list-table-row__content__actions">
          <EventActionButton 
            icon={faEllipsis}
            iconSize='lg'
            onClick={() => {}}
            rounded
          />
        </div>
      </div>
    </div>
  );
}

export default ListTableRow;
