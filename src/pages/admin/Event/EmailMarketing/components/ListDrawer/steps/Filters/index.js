import { useState } from 'react';
import { Form } from 'react-final-form';
import Date from 'src/components/Filter/Inputs/Date';
import Select from 'src/components/Filter/Inputs/Select';
import BaseButton from 'src/components/BaseButton';

import './scss/_main.scss';

const Filters = () => {
  const [filter, setFilter] = useState({
    genderFilter: [],
    birthDateStartFilter: null,
    birthDateEndFilter: null,
  });

  const handleSetFilter = (newFilter) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
  }

  const genderFilterItems = [
    {value: 'all', label: 'Todos'},
    {value: 'female', label: 'Feminino'},
    {value: 'male', label: 'Masculino'},
    {value: 'other', label: 'Outro'}
  ];

  const onSubmit = (values) => {
    console.log(values);
  }

  return (
    <Form onSubmit={onSubmit} render={({ handleSubmit }) => (
      <div className='filters'>
        <p className='filters__description'>Utilize os filtros abaixo se desejar segmentar melhor o público da suas listas de e-mails. Estes filtros só estarão disponíveis caso você tenha solicitado data de nascimento e ou gênero no formulário</p>
        <div className='filters__fields'>
          <div className='filters__fields__row'>
            <Select 
              label='Gênero'
              placeholder='Selecione o gênero'
              items={genderFilterItems} 
              selectedItems={filter.genderFilter}
              onChange={(values) => handleSetFilter({ genderFilter: values })} 
            />
          </div>
          <div className='filters__fields__row'>
            <Date
              label={'Aniversariantes'}
              placeholder={'Data inicial'}
              selectedDate={filter.birthDateStartFilter}
              onChange={(date) => handleSetFilter({ birthDateStartFilter: date })}
              maxDate={filter.birthDateEndFilter}
            />
            <Date 
              placeholder={'Data final'}
              selectedDate={filter.birthDateEndFilter}
              onChange={(date) => handleSetFilter({ birthDateEndFilter: date })}
              minDate={filter.birthDateStartFilter}
            />
          </div>
          <div className='filters__buttons'>
            <BaseButton fullWidth type='submit' onClick={handleSubmit}>Criar lista</BaseButton>
            <BaseButton fullWidth variant='outlined' onClick={() => setOpen(false)}>Cancelar</BaseButton>
          </div>
        </div>
      </div>
    )} />
  );
}

export default Filters;
