import React, { useState } from 'react';
import { Form } from 'react-final-form';
import Select from 'src/components/Filter/Inputs/Select';
import InputField from 'src/components/FormUtils/InputField';
import BaseButton from 'src/components/BaseButton';

import './scss/_main.scss';

const EventList = ({ setOpen, events, onContinue }) => {
  const [eventsFilter, setEventsFilter] = useState([]);

  const onSubmit = (values) => {
    onContinue(values.name, eventsFilter.map(event => event.value));
  }

  return (
    <Form onSubmit={onSubmit} render={({ handleSubmit }) => (
      <div className='event-list'>
        <InputField name='name' label='Nome da lista' placeholder='Insira o nome da lista' autocomplete='off' />
        <Select
          label='Pesquise por nome ou tag'
          placeholder='Pesquise por nome ou tag'
          items={events ? events.map(event => ({value: event.id, label: event.name})) : []} 
          selectedItems={eventsFilter}
          onChange={setEventsFilter}
          isMulti
        />
        <div className='event-list__buttons'>
          <BaseButton fullWidth type='submit' onClick={handleSubmit}>Continuar</BaseButton>
          <BaseButton fullWidth variant='outlined' onClick={() => setOpen(false)}>Cancelar</BaseButton>
        </div>
      </div>
    )} />
  );
}

export default EventList;
