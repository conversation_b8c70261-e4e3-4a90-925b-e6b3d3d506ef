import { Form } from 'react-final-form';
import Checkbox from 'src/components/Checkbox';
import InputField from 'src/components/FormUtils/InputField';
import TextAreaField from 'src/components/FormUtils/TextAreaField';
import BaseButton from 'src/components/BaseButton';

import './scss/_main.scss';

const ManualEmails = ({ setOpen, onSubmit }) => {
  return (
    <Form onSubmit={onSubmit} render={({ handleSubmit }) => (
      <div className='manual-emails'>
        <InputField name='name' label='Nome da lista' placeholder='Insira o nome da lista' />
        <TextAreaField name='list' label='Lista de e-mail' placeholder='Cole aqui sua lista de e-mails com um por linha ou em sequência, separados por vírgulas.' />
        <Checkbox label='Declaro que estes e-mails foram adquiridos de forma legítima sem ferir a Lei Geral de Proteção de Dados.' checked={true} onChange={() => {}} disabled={false} />
        <div className='manual-emails__buttons'>
          <BaseButton fullWidth type='submit' onClick={handleSubmit}>Criar lista</BaseButton>
          <BaseButton fullWidth variant='outlined' onClick={() => setOpen(false)}>Cancelar</BaseButton>
        </div>
      </div>
    )} />
  );
}

export default ManualEmails;
