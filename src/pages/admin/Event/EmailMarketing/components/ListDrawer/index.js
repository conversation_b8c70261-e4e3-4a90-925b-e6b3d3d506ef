import Drawer from 'src/components/NewDrawer';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faArrowLeft, faArrowLeftLong, faClose, faEnvelope, faUserPlus } from '@fortawesome/free-solid-svg-icons'
import ListTypeSelect from '../ListTypeSelect';

import './scss/_main.scss';
import { useState } from 'react';
import ManualEmails from './steps/ManualEmails';
import EventList from './steps/EventList';
import Filters from './steps/Filters';

const STEPS = {
  LIST_TYPE_SELECT: 0,
  EVENT_LIST: 1,
  FILTERS: 2,
  MANUAL_EMAILS: 3
};

const initialValues = {
  name: '',
  emails: [],
  event_id: null,
  list_type: null,
  gender: null,
  age_range_start: null,
  age_range_end: null,
  birthday_start: null,
  birthday_end: null,
  all_ages: true,
  event_ids: [],
  terms: false,
}

const ListDrawer = ({
  open,
  setOpen,
  eventId,
  refetch,
  events
}) => {
  const [step, setStep] = useState(STEPS.LIST_TYPE_SELECT);
  const [form, setForm] = useState({ ...initialValues, event_id: eventId });
  const handleClose = () => {
    setOpen(false);
  };

  const handleListTypeSelect = (listType) => {
    setForm(prev => ({ ...prev, list_type: listType }));

    if (listType === 'MANUAL_EMAILS') {
      setStep(STEPS.MANUAL_EMAILS);
      return;
    }

    setStep(STEPS.EVENT_LIST);
  };
  
  const handleContinue = (name, eventIds) => {
    setForm(prev => ({ ...prev, name, event_ids: eventIds }));
    setStep(STEPS.FILTERS);
  }

  const handleBack = () => {
    if (step === STEPS.MANUAL_EMAILS) {
      setStep(STEPS.LIST_TYPE_SELECT);
      return;
    }

    setStep(step - 1);
  };

  const listTypeOptions = [
    { id: 'EVENT_LIST', label: 'Paticipantes de eventos passados', description: 'Criar lista com o e-mail dos participantes de eventos passados', icon: faUserPlus, onSelect: () => handleListTypeSelect('EVENT_LIST') },
    { id: 'MANUAL_EMAILS', label: 'Adicionar e-mails manualmente', description: 'Digitar ou copiar e colar os e-mails dos participantes da lista', icon: faEnvelope, onSelect: () => handleListTypeSelect('MANUAL_EMAILS') }
  ];

  const steps = {
    [STEPS.LIST_TYPE_SELECT]: {
      icon: null,
      title: 'Criar lista de e-mail',
      rightIcon: <FontAwesomeIcon icon={faClose} size='xl' onClick={handleClose} />,
      content: (
        <>
          <h3 className='list-drawer__title'>Como deseja adicionar o e-mail das pessoas nessa lista?</h3>
          <ListTypeSelect options={listTypeOptions} />
        </>
      )
    },
    [STEPS.EVENT_LIST]: {
      icon: <FontAwesomeIcon icon={faArrowLeftLong} size='xl' onClick={handleBack} />,
      title: 'Participantes de eventos passados',
      rightIcon: <></>,
      content: <EventList setOpen={setOpen} events={events} onContinue={handleContinue} onSubmit={() => console.log(form)} />
    },
    [STEPS.FILTERS]: {
      icon: <FontAwesomeIcon icon={faArrowLeftLong} size='xl' onClick={handleBack} />,
      title: 'Filtros',
      rightIcon: <></>,
      content: <Filters />
    },
    [STEPS.MANUAL_EMAILS]: {
      icon: <FontAwesomeIcon icon={faArrowLeftLong} size='xl' onClick={handleBack} />,
      title: 'Adicionar e-mails manualmente',
      rightIcon: <></>,
      content: <ManualEmails setOpen={setOpen} onSubmit={() => console.log(form)} />
    }
  }

  return (
    <Drawer open={open} setOpen={setOpen} className='list-drawer'>
      <Drawer.Header
        icon={steps[step].icon}
        title={steps[step].title}
        rightIcon={steps[step].rightIcon}
        className='list-drawer__header'
      />
      <Drawer.Body className='list-drawer__body'>
        {steps[step].content}
      </Drawer.Body>
    </Drawer>
  );
}

export default ListDrawer;
