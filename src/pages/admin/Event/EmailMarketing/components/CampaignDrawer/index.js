import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faClose } from '@fortawesome/free-solid-svg-icons'
import Drawer from 'src/components/NewDrawer';
import InputField from 'src/components/FormUtils/InputField';
import Select from 'src/components/Filter/Inputs/Select';
import { Form } from 'react-final-form';
import BaseButton from 'src/components/BaseButton';

import './scss/_main.scss';
import RichEditor from 'src/components/RichEditor';
import { useState } from 'react';

const CampaignDrawer = ({
  open,
  setOpen,
  lists,
  refetch
}) => {
  const [loading, setLoading] = useState(false);
  const [description, setDescription] = useState({
    descriptionDelta: '',
    descriptionHtml: '',
    descriptionText: '',
  });

  const handleChange = (delta, html, text) => {
    setDescription({
      descriptionDelta: delta,
      descriptionHtml: html,
      descriptionText: text,
    });
  }

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Drawer open={open} setOpen={setOpen}>
      <Drawer.Header className='campaign-drawer__header'
        title={'Criar campanha'}
        rightIcon={<FontAwesomeIcon icon={faClose} size='xl' onClick={handleClose} />}/>
      <Drawer.Body className='campaign-drawer__body'>
        <Form onSubmit={() => {}} render={({ handleSubmit }) => (
          <form onSubmit={handleSubmit}>
            <InputField name='name' label='Nome da campanha' />
            <Select
              label='Escolha a lista de e-mail para enviar o convite'
              placeholder='Selecione a lista de e-mails'
              items={lists ? lists.map(list => ({value: list.id, label: list.name})) : []} 
              selectedItems={[]}
              onChange={() => {}}
            />
            <InputField name='subject' label='Assunto' />
            <label className='campaign-drawer__body__label campaign-drawer__body__label--email'>Texto do e-mail</label>
            <RichEditor
              disabled={loading}
              onChange={handleChange}
              delta={description.descriptionDelta}
              imageMaxBytes={5000000}
              imageMimeTypes={['image/jpeg', 'image/png']}
            />
            <div className='campaign-drawer__buttons'>
              <BaseButton fullWidth type='submit' onClick={handleSubmit}>Criar campanha</BaseButton>
              <BaseButton fullWidth variant='outlined' onClick={() => setOpen(false)}>Cancelar</BaseButton>
            </div>
          </form>
        )} />
      </Drawer.Body>
    </Drawer>
  );
}

export default CampaignDrawer;
