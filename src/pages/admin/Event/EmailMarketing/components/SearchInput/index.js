import { useRef } from 'react';
import { faSearch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { withTranslations } from 'lets-i18n';

import './scss/_main.scss';

const SearchInput = ({ t, value, onChange }) => {
  const input = useRef(null);

  return(
    <div className="search-input" onClick={() => input.current.focus()}>
      <FontAwesomeIcon icon={faSearch} />
      <input 
        ref={input} 
        className="search-input__field" 
        type="text" value={value} 
        onChange={(e) => onChange?.(e)} 
        placeholder={'Procurar por nome'} 
      />
    </div>
  );
}

export default withTranslations(SearchInput);
