import EventActionButton from 'src/components/EventActionButton';
import { faEllipsis } from '@fortawesome/free-solid-svg-icons';
import './scss/_main.scss';

const CampaignTableRow = () => {
  return (
    <div className="campaign-table-row">
      <div className="campaign-table-row__content">
        <div className="campaign-table-row__content__title">
          <span>Nome da campanha</span>
          <p>Des<PERSON><PERSON><PERSON> da campanha</p>
        </div>
        <div className="campaign-table-row__content__date">
          <span>Data da criação</span>
          <p>13/01/23 às 14:45</p>
        </div>
        <div className="campaign-table-row__content__scheduled">
          <span>Agendado para</span>
          <p>13/01/23 às 14:45</p>
        </div>
        <div className="campaign-table-row__content__participants">
          <span>Participantes</span>
          <p>348</p>
        </div>
        <div className="campaign-table-row__content__clicks">
          <span>Clicks no botão</span>
          <p>487</p>
        </div>
        <div className="campaign-table-row__content__started_purchase">
          <span>Compras iniciadas</span>
          <p>2898</p>
        </div>
        <div className="campaign-table-row__content__completed_purchase">
          <span>Compras concluídas</span>
          <p>1899</p>
        </div>
        <div className="campaign-table-row__content__list">
          <span>Lista de participantes</span>
          <p>aniversariantes janeiro</p>
        </div>
        <div className="campaign-table-row__content__actions">
          <EventActionButton 
            icon={faEllipsis}
            iconSize='lg'
            onClick={() => {}}
            rounded
          />
        </div>
      </div>
    </div>
  );
}

export default CampaignTableRow;
