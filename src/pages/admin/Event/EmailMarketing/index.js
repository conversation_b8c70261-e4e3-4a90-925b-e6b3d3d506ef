import React, { useState } from 'react'
import { withAuthenticationContext } from 'src/components/AuthenticationProvider';
import Redirect from 'src/components/Redirect'
import Head from 'src/components/Head'
import EmailMarketingCampaigns from './pages/campaigns'
import EmailMarketingLists from './pages/lists'
import { useQuery } from '@apollo/client'
import { eventCampaigns, eventLists, userEvents } from './queries'
import { useRouter } from 'next/router'

import './scss/_main.scss'
import { withTranslations } from 'lets-i18n';

const EmailMarketing = ({ t, authenticationContext }) => {
  const { viewer } = authenticationContext;

  const router = useRouter();
  const [page, setPage] = useState(0);
  
  const emailListsMock = [
    {
      id: 1,
      name: 'Lista 1',
      gender: 'MALE',
      age_range_start: 18,
      age_range_end: 25,
      birthday_start: '2021-01-01',
      birthday_end: '2021-12-31',
      all_ages: false,
      list_type: 'EMAIL',
      total_participations: 100,
      created_at: '2021-01-01T14:45:00Z'
    },
    {
      id: 2,
      name: 'Lista 2',
      gender: 'FEMALE',
      age_range_start: 26,
      age_range_end: 30,
      birthday_start: '2021-01-01',
      birthday_end: '2021-12-31',
      all_ages: false,
      list_type: 'EMAIL',
      total_participations: 200,
      created_at: '2021-01-01T14:45:00Z'
    }
  ];

  const { data: events } = useQuery(userEvents, {
    variables: { userId: viewer.id },
  });

  const { data: campaigns, refetch: refetchCampaigns } = useQuery(eventCampaigns, {
    variables: { eventId: router.query.eventId },
  });

  const { data: lists, refetch: refetchLists } = useQuery(eventLists, {
    variables: { eventId: router.query.eventId },
  });

  return (
    <Redirect.IfNotLogged>
      <Head title={'E-mail Marketing'} />
      {page === 0 
        ? <EmailMarketingCampaigns campaigns={campaigns?.event?.email_campaigns?.nodes ?? []} lists={lists?.event?.email_lists?.nodes ?? emailListsMock} setPage={setPage} refetch={refetchCampaigns} />
        : <EmailMarketingLists lists={lists?.event?.email_lists?.nodes ?? emailListsMock} setPage={setPage} refetch={refetchLists} eventId={router.query.eventId} events={events?.user?.events?.nodes ?? []} />}
    </Redirect.IfNotLogged>
  )
}

export default withTranslations(withAuthenticationContext(EmailMarketing));
