import React, { useState } from 'react'
import EventManager from 'src/components/EventManager'
import EventManagerHeader from 'src/components/EventManager/EventManagerHeader'
import BaseButton from 'src/components/BaseButton'
import EmptyState from 'src/components/EmptyState';
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faClipboardList } from '@fortawesome/free-solid-svg-icons';
import TicketSalesBadge from '../../components/TicketSalesBadge';
import ListTableRow from '../../components/ListTableRow';
import SearchInput from '../../components/SearchInput';
import ListDrawer from '../../components/ListDrawer';

import './scss/_main.scss';

const EmailMarketingLists = ({ lists, setPage, refetch, eventId, events }) => {
  const [open, setOpen] = useState(false);

  return (
    <EventManager activeMenuItem={EventManager.EMAIL_MARKETING}>
      <EventManagerHeader
        sectionTitle={
          <div className='email-marketing-lists__header__title'>
            <FontAwesomeIcon icon={faArrowLeft} color='#085EE5' onClick={() => setPage(0)} />
            <p>LISTA DE E-MAIL</p>
          </div>
        }
        actions={
          <div className='email-marketing-lists__header__actions'>
            <BaseButton colorTheme='success' startIcon={<FontAwesomeIcon icon={faClipboardList} />} onClick={() => setOpen(true)}>Criar nova lista</BaseButton>
          </div>
        }
      />
      <div className='email-marketing-lists__content'>
        {lists.length === 0 ? (
          <EmptyState
            icon={<FontAwesomeIcon icon={faClipboardList} size={'2x'} color="#8BAADF" />} 
            title={'Você ainda não criou uma lista com o e-mail das pessoas que deseja convidar'}
            action={'Criar lista'}
          />
        ) : (
          <>
            <div className='email-marketing-lists__content__header'>
              <SearchInput />
              <TicketSalesBadge
                textColor='#9B24AE'
                backgroundColor='#9B24AE33'
                label='Participantes em lista:'
                value='299.548'
              />
            </div>
            <h3 className='email-marketing-lists__content__title'>Listas</h3>
            <div className='email-marketing-lists__content__list'>
              {lists.map((list) => (
                <ListTableRow key={list.id} list={list} />
              ))}
            </div>
          </>
        )}
      </div>
      <ListDrawer open={open} setOpen={setOpen} refetch={refetch} eventId={eventId} events={events} />
    </EventManager>
  );
}

export default EmailMarketingLists;
