import React, { useState } from 'react'
import EventManager from 'src/components/EventManager'
import EventManagerHeader from 'src/components/EventManager/EventManagerHeader'
import BaseButton from 'src/components/BaseButton'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faClipboardList, faPaperPlane, faVideo } from '@fortawesome/free-solid-svg-icons'

import SearchInput from '../../components/SearchInput'
import TicketSalesBadge from '../../components/TicketSalesBadge'
import CampaignTable from '../../components/CampaignTable'
import CampaignDrawer from '../../components/CampaignDrawer'
import '../../scss/_main.scss'

const EmailMarketingCampaigns = ({ setPage, refetch }) => {
  const [open, setOpen] = useState(false);
  
  return (
    <div>
      <EventManager activeMenuItem={EventManager.EMAIL_MARKETING}>
        <EventManagerHeader
          sectionTitle={'E-mail Marketing'}
          actions={
            <div className='email-marketing__header__actions'>
              <BaseButton variant='outlined' startIcon={<FontAwesomeIcon icon={faVideo} />}>Video Tutorial</BaseButton>
              <BaseButton colorTheme='success' startIcon={<FontAwesomeIcon icon={faClipboardList} />} onClick={() => setPage(1)}>Lista de e-mail</BaseButton>
              <BaseButton startIcon={<FontAwesomeIcon icon={faPaperPlane} />} onClick={() => setOpen(true)}>Criar campanha</BaseButton>
            </div>
          }
        />
        <div className='email-marketing__content'>
          <div className='email-marketing__content__header'>
            <SearchInput />
            <div className='email-marketing__content__header__filters'>
              <TicketSalesBadge
                textColor='#0C8A3E'
                backgroundColor='#6CE99D33'
                label='Envios disponíveis:'
                value='6.548'
              />
            </div>
          </div>
          <div className='email-marketing__content__body'>
            <h3>Campanhas agendadas</h3>
            <CampaignTable />
            <h3>Campanhas enviadas</h3>
            <CampaignTable />
            <h3>Rascunhos</h3>
            <CampaignTable />
          </div>
        </div>
      </EventManager>
      <CampaignDrawer open={open} setOpen={setOpen} refetch={refetch} />
    </div>
  );
};

export default EmailMarketingCampaigns;
