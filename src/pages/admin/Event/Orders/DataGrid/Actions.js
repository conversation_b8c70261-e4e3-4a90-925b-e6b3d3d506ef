import React from 'react'
import DataGrid from 'src/components/DataGrid'
import orderItemClassName from './orderItemClassName'

const isOffline = (type) => {
  const types = [
    'OFFLINE',
    'OFFLINE_BANK_SLIP',
    'OFFLINE_CASH',
    'OFFLINE_CREDIT',
    'OFFLINE_DEBIT',
    'OFFLINE_DEPOSIT',
    'COMPLIMENTARY_TICKETS',
  ]
  return types.includes(type)
}

const Actions = ({
  order,
  t,
  downloadPdf,
  pdfLoading,
  displayOrderDetails,
  cancelOrder,
  cancelAndRefundOrder,
}) => (
  <DataGrid.Actions className={`${orderItemClassName}-actions`}>
    <DataGrid.Action
      text={t('eventOrders.actions.viewDetails')}
      icon="panel-stats"
      onClick={() => displayOrderDetails(order)}
      outside
    />
    {order.state_detail === 'PENDING_BANK_SLIP' && (
      <DataGrid.Action
        text={t(`eventOrders.actions.downloadBankSlip`)}
        icon="download"
        onClick={() => window.open(order.payment_summary.external_invoice_url)}
      />
    )}
    <DataGrid.Action
      text={t('eventOrders.actions.ticketDownload')}
      disabled={!(order.state === 'PAID')}
      loading={pdfLoading}
      icon="download"
      onClick={() => downloadPdf(order.id)}
    />

    {isOffline(order.payment_type) && (
      <DataGrid.Action
        text={t('eventOrders.actions.cancelPurchase')}
        icon="disable"
        onClick={() => cancelOrder(order)}
      />
    )}

    {(order.state === 'IN_PROCESS' ||
      ((order.payment_type === 'FREE' || order.payment_type === 'PDV') &&
        order.state === 'PAID')) && (
      <DataGrid.Action
        text={t(
          `eventOrders.actions.${
            order.payment_type === 'BANK_SLIP'
              ? 'cancelBankslip'
              : 'cancelPurchase'
          }`
        )}
        icon="disable"
        onClick={() => cancelOrder(order)}
      />
    )}

    {order.state === 'PAID' &&
      ['WALLET', 'CREDIT', 'PIX', 'BANK_SLIP'].includes(order.payment_type) &&
      !isOffline(order.payment_type) && (
        <DataGrid.Action
          text={t('eventOrders.actions.cancelAndRefund')}
          icon="disable"
          onClick={() => cancelAndRefundOrder(order)}
        />
      )}
  </DataGrid.Actions>
)

export default Actions
