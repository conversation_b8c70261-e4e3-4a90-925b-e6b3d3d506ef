import React from 'react'
import DataGrid from 'src/components/DataGrid'
import { get, filter } from 'lodash'
import { Text } from '@blueprintjs/core'
import withToggleStatus from 'src/components/WithToggleStatus'

import orderItemClassName from './orderItemClassName'

const maxParticipantsToDisplay = 2

const Participants = ({ order, t, isToggleActive, toggleStatus }) => {
  const unfilteredParticipants = get(order, 'purchase_items.nodes', [])

  // use filter to create a new array, since we cannot remove items from the
  // array returned by apollo (readonly)
  const participants = filter(
    unfilteredParticipants,
    (item) => item.participant
  )

  const classNameBlock = `${orderItemClassName}-participants`

  const isPDV = order?.payment_type === 'PDV'

  return (
    <DataGrid.Cell
      label={isPDV ? 'Quantidade de ingressos' : t('eventOrders.participants')}
      className={classNameBlock}
    >
      <Text className={`${classNameBlock}__text`}>
        {isPDV ? (
          <>{participants.length}</>
        ) : (
          <>
            {(!isToggleActive
              ? participants.slice(0, maxParticipantsToDisplay)
              : participants
            )
              .map((p) => p.participant.name)
              .join(', ')}

            {participants.length === 0 && <span>-</span>}

            {!isToggleActive &&
              participants.length > maxParticipantsToDisplay &&
              '...'}
          </>
        )}
      </Text>

      {!isPDV && participants.length > maxParticipantsToDisplay && (
        <div className={`${classNameBlock}__more`} onClick={toggleStatus}>
          {!isToggleActive
            ? `${t('eventOrders.viewAll')} (${participants.length})`
            : t('eventOrders.hide')}
        </div>
      )}
    </DataGrid.Cell>
  )
}

export default withToggleStatus(Participants)
