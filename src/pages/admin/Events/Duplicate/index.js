import React, { Component } from 'react'
import moment from 'moment'
import 'moment/locale/pt'
import { Intent } from '@blueprintjs/core'
import { eventAdminPath, pushRoute } from 'src/utils/routes'
import { withTranslations as wt } from 'lets-i18n'
import Drawer from 'src/components/NewDrawer'
import {
  dateFromStringIgnoringOffset,
  dateToStringOnGivenUtcOffset,
  older,
} from 'src/utils/dateTime'
import Toaster from 'src/components/Toaster'
import { rollbar } from 'config/rollbar'
import { get } from 'lodash'
import {
  eventDetails as query,
  duplicateEvent as duplicateEventMutation,
} from './queries'
import Panel from './components'
import './scss/main.scss'

const parseDate = (date, utcOffset) => {
  if (!date) return null
  return dateToStringOnGivenUtcOffset(date, utcOffset)
}
class ValidationError extends Error {
  constructor(validationErrors) {
    super()
    this.message = validationErrors
      .reduce((acc, errorMessage, i) => {
        acc.push(`${i + 1}. ${errorMessage}`)
        return acc
      }, [])
      .join('\n')
  }
}

class DuplicateEvent extends Component {
  static initialState = {
    isLoading: true,
    eventName: '',
    duplicating: false,
    form: {
      name: '',
      duplicateLists: true,
      duplicateTickets: true,
      startsAtMinDate: moment()
        .startOf('minute')
        .toDate(),
      endsAtMinDate: moment()
        .startOf('minute')
        .toDate(),
    },
  }

  state = {
    ...DuplicateEvent.initialState,
  }

  static getDerivedStateFromProps(props, state) {
    const { isOpen } = props
    const { isLoading } = state
    if (!isOpen && !isLoading) {
      return { ...DuplicateEvent.initialState }
    }
    return null
  }

  async componentDidUpdate() {
    const { id, isOpen } = this.props
    const { form, isLoading } = this.state

    if (!isOpen || !isLoading) {
      return
    }

    try {
      const event = await query(id)
      const { name, starts_at, utc_offset } = event
      const startsAt = older(
        dateFromStringIgnoringOffset(starts_at),
        dateFromStringIgnoringOffset(moment().format())
      )
      this.setState({
        isLoading: false,
        eventName: name,
        event,
        form: {
          ...form,
          eventId: id,
          name,
          startsAt,
          endsAt: startsAt,
          duplicateLists: true,
          duplicateTickets: true,
          utcOffset: utc_offset,
          endsAtMinDate: startsAt,
        },
      })
    } catch (err) {
      rollbar.error(err)
    }
  }

  handleInputChange = (key, value) => {
    this.setState(({ form, startsAtError, endsAtError }) => ({
      form: {
        ...form,
        endsAtMinDate:
          key === 'startsAt' ? value || new Date() : form.endsAtMinDate,
        [key]: value,
      },
      startsAtError: key === 'startsAt' && value ? null : startsAtError,
      endsAtError: key === 'endsAt' && value ? null : endsAtError,
    }))
  }

  duplicateEvent = async () => {
    const { t } = this.props
    const { form } = this.state
    const { startsAt, endsAt, utcOffset } = form

    if (startsAt == null) {
      this.setState({
        startsAtError: t('duplicateEvent.startsAtErrorIsRequired'),
      })
    }
    if (endsAt == null) {
      this.setState({ endsAtError: t('duplicateEvent.endsAtErrorIsRequired') })
    }
    if (!startsAt || !endsAt) {
      return
    }

    this.setState({ duplicating: true })

    const variables = {
      ...form,
      startsAt: parseDate(startsAt, utcOffset),
      endsAt: parseDate(endsAt, utcOffset),
    }

    try {
      const data = await duplicateEventMutation(variables)

      const errors = get(data, 'duplicate_event.errors', undefined)
      if (errors && errors.length > 0) {
        throw new ValidationError(errors)
      }

      const path = eventAdminPath(data.duplicate_event.slug)
      this.setState({ duplicating: true })

      Toaster.show({
        message: t('duplicateEvent.duplicateSuccessMessage'),
        intent: Intent.SUCCESS,
      })

      setTimeout(() => {
        pushRoute({ path })
      }, 500)
    } catch (error) {
      this.setState({ duplicating: false })

      if (error instanceof ValidationError) {
        Toaster.show({
          message: error.message,
          intent: Intent.DANGER,
        })
      } else {
        rollbar.error(error)
      }
    }
  }

  render() {
    const { isOpen, onClose, t } = this.props
    const { endsAtError, startsAtError } = this.state

    return (
      <React.Fragment>
        <Drawer open={isOpen} toggleDrawer={onClose}>
          <Drawer.Header
            title={t('duplicateEvent.duplicateEvent')}
          />
          <Drawer.Body>
            <Panel
              {...this.props}
              {...this.state}
              duplicateEvent={this.duplicateEvent}
              handleInputChange={this.handleInputChange}
              endsAtError={endsAtError}
              startsAtError={startsAtError}
            />
          </Drawer.Body>
        </Drawer>
      </React.Fragment>
    )
  }
}

export default wt(DuplicateEvent)
