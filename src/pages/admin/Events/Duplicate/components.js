import React from 'react'
import { Checkbox } from '@blueprintjs/core'
import { withTranslations as wt } from 'lets-i18n'

import FormGroup from 'src/components/FormGroup'
import InlineDateTimeInput from 'src/components/Input/InlineDateTimeInput'
import { Input } from 'src/components/Input/Input'
import ButtonGroup from 'src/components/ButtonGroup'

import { ErrorsCallout } from 'src/components/Callout'
import { Loading } from 'src/components/NonIdealState'
import 'src/utils/scss/form.scss'
import Spacing from 'src/components/Spacing'

const NameInput = wt(({ t, name, onChange }) => (
  <FormGroup label={t('duplicateEvent.newEventName')} labelFor="newEventName">
    <Input
      id="newEventName"
      className="duplicate-event__text-input"
      value={name}
      name="newEventName"
      onChange={onChange}
      maxChars={100}
    />
  </FormGroup>
))

const DateInputs = wt(
  ({ onChange, t, startsAt, endsAt, startsAtMinDate, endsAtMinDate }) => {

    return (
      <React.Fragment>
        <div className="new-form-layout">
          <FormGroup label={t('duplicateEvent.newEventStart')}>
            <InlineDateTimeInput
              value={startsAt}
              onChange={(date) => onChange('startsAt', date)}
              minDate={startsAtMinDate}
              // maxDate={endsAt}
            />
          </FormGroup>
          <Spacing size={24} />
          <FormGroup label={t('duplicateEvent.newEventEnd')}>
            <InlineDateTimeInput
              value={endsAt}
              onChange={(date) => onChange('endsAt', date)}
              minDate={startsAt}
              initialMonth={startsAt || new Date()}
            />
          </FormGroup>
        </div>
      </React.Fragment>
    )
  }
)

const Panel = ({
  onClose,
  isLoading,
  duplicating,
  form,
  t,
  duplicateEvent,
  handleInputChange,
  endsAtError,
  startsAtError,
}) => {
  if (isLoading) return <Loading />

  return (
    <div className="new-form-layout">
      <Spacing size={10} />
      <NameInput
        name={form.name}
        onChange={(newName) => {
          handleInputChange('name', newName)
        }}
      />
      <Spacing size={20} />
      <DateInputs
        startsAt={form.startsAt}
        endsAt={form.endsAt}
        startsAtMinDate={form.startsAtMinDate}
        endsAtMinDate={form.endsAtMinDate}
        onChange={(key, value) => handleInputChange(key, value)}
      />
      <FormGroup>
        <Checkbox
          checked={form.duplicateTickets}
          label={t('duplicateEvent.duplicateTickets')}
          onChange={(e) =>
            handleInputChange('duplicateTickets', e.target.checked)
          }
        />
        <Checkbox
          checked={form.duplicateLists}
          label={t('duplicateEvent.duplicateLists')}
          onChange={(e) =>
            handleInputChange('duplicateLists', e.target.checked)
          }
        />
      </FormGroup>
      {(endsAtError || startsAtError) && (
        <ErrorsCallout
          title={t('duplicateEvent.errorsCalloutTitle')}
          errors={[startsAtError, endsAtError].filter(Boolean)}
        />
      )}
      <ButtonGroup>
        <ButtonGroup.PrimaryButton
          loading={duplicating}
          onClick={duplicateEvent}
        >
          {t('duplicateEvent.duplicateEvent')}
        </ButtonGroup.PrimaryButton>
        <ButtonGroup.SecondaryButton onClick={onClose}>
          {t('global.formButtonCancel')}
        </ButtonGroup.SecondaryButton>
      </ButtonGroup>
    </div>
  )
}

export default wt(Panel)
