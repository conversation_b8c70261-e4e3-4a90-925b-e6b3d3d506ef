import { get } from 'lodash'
import {
  eventAdminPath,
  eventCheckinPath,
  reportSalesPath,
} from 'src/utils/routes'

export const participantsEstimations = event => {
  const reports = event.reports
  if (!reports) return { list: 0, total: 0 }

  const soldCount = reports.tickets.sold_count
  const listsParticipantsCount = reports.lists.participations_count

  const listDetailString =
    listsParticipantsCount > 0 ? listsParticipantsCount : '-'
  const totalDetailString =
    listsParticipantsCount + soldCount > 0
      ? listsParticipantsCount + soldCount
      : '-'

  return { list: listDetailString, total: totalDetailString }
}

const roles = {
  admin: 'MASTER_ADMINS',
  superAdmin: 'SUPER_ADMIN',
  doormen: 'DOORMEN',
  sales_analysts: 'SALES_ANALYSTS',
  pdv: "PDV_OPERATOR"
}

export const isAdmin = event => {
  const userRoles = get(event, 'viewer_access_policy.user_roles', [])
  return userRoles.includes(roles.admin) || userRoles.includes(roles.superAdmin)
}

export const isSalesAnalyst = event => {
  const userRoles = get(event, 'viewer_access_policy.user_roles', [])
  return userRoles.includes(roles.sales_analysts)
}

export const isDoorMen = event => {
  const userRoles = get(event, 'viewer_access_policy.user_roles', [])
  return userRoles.includes(roles.doormen)
}

export const isPDVOperator = event => {
  const userRoles = get(event, 'viewer_access_policy.user_roles', [])
  return userRoles.includes(roles.pdv)
}

export const eventUserRolePath = event => {
  if (isAdmin(event)) return eventAdminPath(event.slug)
  if (isSalesAnalyst(event)) return reportSalesPath(event.slug)
  if (isDoorMen(event)) return eventCheckinPath(event.slug)
}
