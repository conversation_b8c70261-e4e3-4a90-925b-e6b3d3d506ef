import moment from 'moment'
import 'moment/locale/pt'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { getManageEventLink, TicketsProgressBar } from '../components'
import { isAdmin, isDoorMen, isPDVOperator, isSalesAnalyst } from '../functions'
import { withTranslations } from 'lets-i18n'
import EventActionButton from '../EventActionButton'
import { faEllipsisH } from '@fortawesome/free-solid-svg-icons'
import Dropdown from 'src/components/DropdownNew'
import {
  eventCheckinPath,
  reportSalesPath,
  eventDetailsPath,
  eventPDVPath,
} from 'src/utils/routes'
import './style.scss'

const EventItem = ({ event, t, duplicate, share, remove }) => {
  const [eventActionOpen, setEventActionOpen] = useState(false)
  const [canClickEvent, setCanClickEvent] = useState(true)
  const router = useRouter()

  useEffect(() => {
    let timeoutId

    if (!eventActionOpen) {
      setCanClickEvent(false)
      timeoutId = setTimeout(() => {
        setCanClickEvent(true)
      }, 200)
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [eventActionOpen])

  const placeString =
    event.location_type === 'PHYSICAL'
      ? `${event.address.one_line}`
      : t(`global.event.locationType.${event.location_type}`)

  const handleGoToPDV = (event) => {
    const token = event.share_link.split('?t=')[1]

    const url = eventPDVPath(event.slug) + (token ? `?t=${token}` : '')

    window.open(url, '_blank')
  }

  const dropdownOptions = [
    {
      type: 'TITLE',
      title: 'Gerenciar',
    },
    {
      title: t('myEvents.goToCheckIn'),
      onClick: () => window.open(eventCheckinPath(event.slug), '_blank'),
      hide: !isDoorMen(event) && !isAdmin(event),
    },
    {
      title: t('myEvents.goToPDV'),
      onClick: () => handleGoToPDV(event),
      hide: !isPDVOperator(event) && !isAdmin(event),
    },
    {
      title: t('myEvents.viewReports'),
      onClick: () => router.push(reportSalesPath(event.slug)),
      hide: !isSalesAnalyst(event) && !isAdmin(event),
    },
    {
      type: 'DIVIDER',
    },
    {
      type: 'TITLE',
      title: 'Configurações',
    },
    {
      title: t('myEvents.view'),
      onClick: () => window.open(event.share_link, '_blank'),
    },
    {
      title: t('myEvents.duplicate'),
      onClick: () => duplicate(event.id),
      hide: !isAdmin(event),
    },
    {
      title: t('myEvents.share'),
      onClick: () => share(event),
    },
    {
      title: t('myEvents.delete'),
      onClick: () => remove(event),
      hide: !isAdmin(event),
      color: '#CF3C4F',
    },
  ]

  const handleEventClick = (e) => {
    if (!isAdmin(event) || !canClickEvent) return

    router.push(getManageEventLink(event))
  }

  return (
    <div className="event-item" onClick={handleEventClick}>
      <div className="event-item__info">
        <img src={event.photo.small_url} className="event-item__info__photo" />
        <div className="event-item__info__texts">
          <p className="event-item__info__texts__name">{event.name}</p>
          <div className="event-item__info__texts__location">{placeString}</div>
        </div>
      </div>
      <div className="event-item__date">
        <div className="event-item__date__title">{t('myEvents.date')}</div>
        <div className="event-item__date__period">
          {event.starts_at
            ? moment(event.starts_at).format('DD/MM/YYYY')
            : `${t('global.event.startsAt.toBeDefined')}`}
        </div>
      </div>
      <div className="event-item__tickets">
        <TicketsProgressBar event={event} />
      </div>
      <div className="event-item__actions">
        <Dropdown
          options={dropdownOptions}
          open={eventActionOpen}
          setOpen={setEventActionOpen}
          dense
        >
          <EventActionButton icon={faEllipsisH} rounded />
        </Dropdown>
      </div>
    </div>
  )
}

export default withTranslations(EventItem)
