import { gql } from '@apollo/client'

export const getUserConfig = gql`
  {
    viewer {
      id
      config {
        checkout {
          ticket {
            one_participation_per_document {
              active
              scope
            }
          }
          purchase {
            accepts_foreign_card
            boleto_expiration_days_from_now
            notify_each_purchase
            require_invoice_info
            disable_bank_slip
          }
        }
      }
    }
  }
`

export const userConfigSet = gql`
  mutation UserConfigSetMutation($input: UserConfigSetInput!) {
    user_config_set(input: $input) {
      ... on User {
        config {
          checkout {
            ticket {
              one_participation_per_document {
                active
                scope
              }
            }
            purchase {
              accepts_foreign_card
              boleto_expiration_days_from_now
              notify_each_purchase
              require_invoice_info
              disable_bank_slip
            }
          }
        }
      }
      ... on ValidationErrors {
        errors
      }
    }
  }
`
