import { useMutation, useQuery } from '@apollo/client'
import AdminTemplate from 'src/components/Templates/Admin'
import { getUserConfig, userConfigSet } from './queries'
import ConfigurationWrapper from '../Event/Configuration/ConfigurationWrapper'
import { Loading } from 'src/components/NonIdealState'
import { Container, Text } from '@lets-events/react'

const AdmConfigPage = () => {
  const { data, loading: loadingInitialRequest } = useQuery(getUserConfig)

  const [setUserConfig] = useMutation(userConfigSet)

  const userID = data?.viewer?.id

  const handleSaveConfig = async (configValues) => {
    const {
      require_invoice_info,
      notify_each_purchase,
      one_participation_per_document,
      accepts_foreign_card,
      boleto_expiration_days_from_now,
      disable_bank_slip,
    } = configValues

    const input = {
      user_id: userID,
      checkout: {
        ticket: {
          one_participation_per_document: {
            active: one_participation_per_document,
            scope: 'EVENT',
          },
        },
        purchase: {
          require_invoice_info,
          notify_each_purchase,
          accepts_foreign_card,
          boleto_expiration_days_from_now,
          disable_bank_slip,
        },
      },
    }

    const r = await setUserConfig({ variables: { input } })

    const checkout = r?.data?.user_config_set?.config?.checkout

    return checkout
  }

  const checkout = data?.viewer?.config?.checkout

  return (
    <AdminTemplate>
      <div
        style={{
          backgroundColor: 'white',
          height: '66px',
          padding: '0 28px 0 20px',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'center',
        }}
      >
        <Text typography="headline6" fontWeight="semibold">
          CONFIGURAÇÕES
        </Text>
      </div>
      <ConfigurationWrapper
        handleSaveConfig={handleSaveConfig}
        checkout={checkout}
        loading={loadingInitialRequest}
      />
    </AdminTemplate>
  )
}

export default AdmConfigPage
