import React, {
  createContext,
  useContext,
  useState,
  useMemo,
  useEffect,
} from 'react'
import useQZPrint from './useQZPrint'
import Dialog from 'src/components/CreateEventFlow/CreateEventModal/Dialog'
import Select from 'src/components/Select'
import 'src/utils/scss/form.scss'
import Spacing from 'src/components/Spacing'
import BaseButton from 'src/components/BaseButton'
import { Loading } from 'src/components/NonIdealState'
import FormGroup from 'src/components/FormGroup'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faCircleXmark } from '@fortawesome/free-solid-svg-icons'
import DialogStatus from 'src/pages/participant/MyTickets/MyTicketsCard/RefundDialog/DialogStatus'

const PrintContext = createContext()

export const PrintProvider = ({ children, startWithOpenModal }) => {
  const [configModalOpen, setConfigModalOpen] = useState(startWithOpenModal)

  const {
    closeConnection,
    findPrinters,
    initConnection,
    loading,
    print,
    printerList,
    setSelectedPrinter,
    selectedPrinter,
    error,
  } = useQZPrint()

  const printerOptions = useMemo(() => {
    return printerList.map((name) => ({ value: name, text: name }))
  }, [printerList])

  useEffect(() => {
    if (startWithOpenModal) {
      InitPrinter()
    }
  }, [startWithOpenModal])

  useEffect(() => {
    return () => {
      closeConnection()
    }
  }, [])

  const InitPrinter = async () => {
    await initConnection()
    await findPrinters()
  }

  const noPrinterFound = printerOptions.length === 0

  return (
    <PrintContext.Provider
      value={{
        print,
        selectedPrinter,
      }}
    >
      <Dialog isOpen={configModalOpen}>
        {loading ? (
          <Loading />
        ) : noPrinterFound || error ? (
          <>
            <DialogStatus status="error" />
            <p className="credit-error-dialog__title">
              {error ?? 'Nenhuma impressora encontrada'}
            </p>
            <Spacing size={16} />
            <BaseButton onClick={InitPrinter} fullWidth>
              Tentar novamente
            </BaseButton>
            <Spacing size={16} />
            <BaseButton
              variant="text"
              fullWidth
              onClick={() => setConfigModalOpen(false)}
            >
              Continuar sem impressora
            </BaseButton>
          </>
        ) : (
          <>
            <div className="new-form-layout">
              <FormGroup label="Impressora">
                <Select
                  value={selectedPrinter}
                  options={printerOptions}
                  onChange={(e) => {
                    setSelectedPrinter(e)
                  }}
                />
              </FormGroup>
            </div>
            <Spacing size={16} />
            <BaseButton onClick={() => setConfigModalOpen(false)} fullWidth>
              Salvar
            </BaseButton>
          </>
        )}
      </Dialog>
      {children}
    </PrintContext.Provider>
  )
}

export const usePrinter = () => {
  const context = useContext(PrintContext)

  if (context == null) {
    console.error('PrintProvider not found')
  }

  return context
}
