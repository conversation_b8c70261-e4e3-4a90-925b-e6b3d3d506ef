import { useState } from 'react'
import qz from 'qz-tray'
import PromiseNotification from 'src/components/PromiseNotification'

const useQZPrint = () => {
  const [loading, setLoading] = useState()
  const [printerList, setPrinterList] = useState([])
  const [selectedPrinter, setSelectedPrinter] = useState('')
  const [error, setError] = useState()

  const initConnection = async () => {
    if (qz.websocket.isActive()) return
    setLoading(true)
    setError(undefined)
    try {
      const data = await PromiseNotification(
        qz.websocket.connect(),
        'Estabelecendo conexão com o QZ Tray...',
        'Conexão estabelecida com sucesso.'
      )

      if (data?.error) {
        setError(data?.error?.toString())
      }
    } catch (err) {
      setError(err)
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const findPrinters = async () => {
    if (!qz.websocket.isActive()) return

    setLoading(true)
    setError(undefined)
    try {
      var data = await PromiseNotification(
        qz.printers.find(),
        'Buscando impressoras...',
        'Busca de impressoras finalizada.'
      )

      if (data?.error) {
        setError(data?.error?.toString())
      } else {
        const list = data?.reverse() ?? ['']
        setPrinterList(list)
        setSelectedPrinter(list[0])
      }
    } catch (err) {
      setError(err)
      console.error(err)
    } finally {
      setLoading(false)
    }
  }

  const closeConnection = async () => {
    if (!qz.websocket.isActive()) return
    try {
      await qz.websocket.disconnect()
    } catch (err) {
      console.error(err)
    }
  }

  const print = async (data, configOptions) => {
    setError(undefined)
    try {
      if (!qz.websocket.isActive()) {
        await initConnection()
      }
      const config = qz.configs.create(selectedPrinter, configOptions)
      await qz.print(config, data)
    } catch (err) {
      setError(err)
      console.error(err)
    }
  }

  return {
    initConnection,
    findPrinters,
    closeConnection,
    setSelectedPrinter,
    print,
    loading,
    printerList,
    selectedPrinter,
    error,
  }
}

export default useQZPrint
