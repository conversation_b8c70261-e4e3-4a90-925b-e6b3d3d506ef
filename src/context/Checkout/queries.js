import { gql } from '@apollo/client'

export const getEventOfferQuery = gql`
  query eventOffer($eventId: ID!, $token: String, $code: String) {
    event_offer(id: $eventId, token: $token) {
      id
      starts_at
      ends_at
      address {
        one_line
      }
      slug
      clearsale_active
      seating_chart
      published
      discount_coupons_present
      total_capacity
      quantity_unavailable_tickets
      name
      checkout_configuration {
        require_invoice_info
      }
      contact_information {
        name
        has_email
      }
      simple_member_get_member
      share_link
      photo {
        event_id
        small_url
      }
      available_payment_methods {
        payment_type
        max_installments
        max_installments_without_interest
      }
      donation_campaign_offers {
        nodes {
          id
          description
          minimum_donation
          name
          public
          sales_state
        }
      }
      categories {
        nodes {
          id
          tickets {
            nodes {
              id
            }
          }
        }
      }
      ticket_offers(discount_code: $code) {
        sales_state
        nodes {
          id
          batches_quantity
          display_only_available_batches
          name
          description
          sales_state
          seats_category
          public
          category {
            description
            id
            name
            photo {
              thumb_url
              original_url
            }
          }
          batches {
            id
            number
            price
            price_with_discount
            purchaseable_quantities
            remaining_amount
            sales_state
            payment_methods {
              due_amount
              due_service_fee
              payment_type
            }
          }
          form {
            name
            additional_fields {
              address {
                mandatory
                requested
              }

              birthday {
                mandatory
                requested
              }
              cnpj {
                mandatory
                requested
              }
              company {
                mandatory
                requested
              }
              cpf {
                mandatory
                requested
              }
              email {
                mandatory
                requested
              }
              gender {
                mandatory
                requested
              }
              identity_document_number {
                mandatory
                requested
              }
              job_title {
                mandatory
                requested
              }
              linkedin {
                mandatory
                requested
              }
              phone_number {
                mandatory
                requested
              }
              website {
                mandatory
                requested
              }
              whatsapp {
                mandatory
                requested
              }
            }
            questions {
              id
              required
              title
              choices
              answer_mode
            }
          }
        }
      }
      package_offers {
        nodes {
          id
          name
          price
          sales_state
          purchaseable_quantities
          max_quantity_available_for_sale
          package_ticket_offers {
            id
            ticket_name
            quantity
          }
          payment_methods {
            due_amount
            due_service_fee
            payment_type
          }
          tickets {
            nodes {
              id
              name
              total_available
            }
          }
        }
      }
      affiliate_link {
        active
        present
        tag
        program_enablement {
          status
        }
      }
    }
    viewer {
      id
      email
      first_name
      phone_number
      last_name
      cpf
      gender
      birthday
      addresses {
        nodes {
          city
          complement
          country
          name
          neighborhood
          number
          state
          street
          zip_code
          one_line
        }
      }
    }
  }
`

export const cartSummaryQuery = gql`
  query cartSummary(
    $ticketPurchaseId: ID!
    $paymentMethod: TicketPaymentMethod!
  ) {
    node(id: $ticketPurchaseId, node_type: TICKET_PURCHASE) {
      ... on TicketPurchase {
        id
        state
        state_detail
        cart_summary(payment_method: $paymentMethod) {
          total_buyer_price
          total_service_fee_passed_to_buyer
          total_discount_amount
          total_service_fee
          total_quantity

          cart_items {
            name
            quantity
            unit_price
            seats
          }
        }
      }
    }
  }
`

export const verifyPaymentMutation = gql`
  mutation verifyPayment($input: TicketOfficePurchaseVerifyPaymentInput!) {
    ticket_office_purchase_verify_payment(input: $input) {
      ... on Payment {
        payment_state
      }
      ... on ValidationErrors {
        errors
      }
    }
  }
`
