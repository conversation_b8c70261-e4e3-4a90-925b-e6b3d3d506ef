import { useMutation, useQuery } from '@apollo/client'
import { withRouter } from 'next/router'
import React, {
  createContext,
  useCallback,
  useEffect,
  useReducer,
  useRef,
  useState,
} from 'react'
import checkoutReducer from 'src/reducers/checkout'
import {
  calculateMaxPackages,
  mergeLocalTicketsWithStateTickets,
  allPackageTicketSelectionsByTicketName,
} from 'src/utils/tickets'
import {
  cartSummaryQuery,
  getEventOfferQuery,
  verifyPaymentMutation,
} from './queries'
import { ticketsNormalizationFromQuery } from '../utils'
import { useTimerCountDown } from './useTimer'
import { eventPath, myEventsPath } from 'src/utils/routes'
import { useIntegrationsQuery } from 'src/pages/participant/Event/old/Page/queries'
import { useFacebookPixel } from 'src/hooks/useFacebookPixel'
import { useAnalyticsInstance } from 'src/hooks/useAnalyticsInstance'
import { useGTMIntegration } from 'src/hooks/useGTMIntegration'

const CheckoutContext = createContext()

const checkoutStorageKey = '--checkout-state'

export const STEP = {
  Ticket: 'ticket',
  Form: 'form',
  Invoice: 'invoice',
  Payment: 'payment',
  PixPaymentForm: 'pixpaymentform',
  CreditCardForm: 'creditcardform',
  CreditCardOwnerForm: 'creditcardownerform',
  InternationalCardForm: 'internationalcardform',
  BankSlipForm: 'bankslipform',
  Result: 'result',
  PixResult: 'pixResult',
  BankSlipResult: 'bankslipresult',
  TimeoutResult: 'timeoutresult',
  PDVPreview: 'pdvpreview',
}

const initialState = {
  step: STEP.Ticket,
  formData: {},
  paymentData: {},
  coupon: null,
  tickets: [],
  donations: [],
  user: null,
  previousStepList: [],
  cartSummary: {},
  donationCart: [],
  serverCartSummary: null,
  purchase: null,
  deliverToMail: '',
  payInfo: null,
  creditInfo: null,
  discountCode: null,
  payMethod: null,
  participantData: null,
  event: {},
}

const zeroPad = (num, places) => String(num).padStart(places, '0')

const stopTimerStepList = [STEP.Ticket, STEP.Result, STEP.BankSlipResult]

const CheckoutProvider = withRouter(({ children, router, isPDV }) => {
  const [loadingEventOffer, setloadingEventOffer] = useState(true)
  const [saveState, setSaveState] = useState()
  const [state, dispatch] = useReducer(checkoutReducer, initialState)
  const [pop, setPop] = useState()

  const slug = router?.asPath.split('/')[2]

  const {
    query: { t: token, tracking_list: trackingLinkID },
  } = router

  const { integrations } = useIntegrationsQuery(slug)
  const { trackEvent } = useFacebookPixel(integrations)
  const analyticsInstance = useAnalyticsInstance(integrations, slug)
  const { pushToDataLayer } = useGTMIntegration(integrations)

  const { refetch: refetchEventOffer } = useQuery(getEventOfferQuery, {
    variables: { eventId: slug, token },
    skip: true,
  })

  const { refetch: refetchCartSummary } = useQuery(cartSummaryQuery, {
    skip: true,
  })

  const [verifyPaymentRequest, { loading }] = useMutation(verifyPaymentMutation)

  const isPurchasePaid = async (purchaseId) => {
    try {
      const result = await verifyPaymentRequest({
        variables: {
          input: {
            ticket_purchase_id: purchaseId,
          },
        },
      })

      const paymentState =
        result?.data?.ticket_office_purchase_verify_payment?.payment_state

      if (paymentState === 'PAID') {
        return true
      } else {
        return false
      }
    } catch {
      return false
    }
  }

  const updateServerCart = async (paymentType, id) => {
    try {
      const result = await refetchCartSummary({
        ticketPurchaseId: id ?? state.purchase.id,
        paymentMethod: paymentType,
      })

      const cart = result?.data?.node?.cart_summary

      if (cart) setServerCart(cart)
    } catch {}
  }

  const fetchEventOffer = async (code) => {
    setloadingEventOffer(true)

    try {
      const r = await refetchEventOffer({
        code,
      })
      const data = r.data

      let tickets = ticketsNormalizationFromQuery(data?.event_offer)
      setTickets(tickets)
      setUser(data?.viewer)
      setEvent(data?.event_offer)
      setDonations(data?.event_offer?.donation_campaign_offers?.nodes ?? [])
    } catch (err) {
      console.log(err)
    } finally {
      setloadingEventOffer(false)
    }
  }

  const popCount = useRef(-3)

  useEffect(() => {
    if (isPDV) return
    window.history.pushState(null, '', window.location.href)
    window.history.back()
    window.history.forward()

    function listen(ev) {
      window.history.go(1)

      if (popCount.current < 0) {
        popCount.current++
        return
      }

      setPop(true)

      popCount.current = -1
    }

    window.addEventListener('popstate', listen)

    return () => {
      window.removeEventListener('popstate', listen)
    }
  }, [isPDV])

  useEffect(() => {
    if (pop) {
      setToPreviousStep()
      setPop(false)
    }
  }, [pop])

  useEffect(() => {
    return () => {
      stopTimer()
    }
  }, [])

  const setCategories = (categories) => {
    dispatch({ type: 'SET_CATEGORIES', payload: categories })
  }

  const setHoldToken = (token) => {
    dispatch({ type: 'SET_HOLD_TOKEN', payload: token })
  }

  const setParticipantData = (data) => {
    dispatch({ type: 'SET_PARTICIPANT_DATA', payload: data })
    setSaveState(true)
  }

  const setInstallments = (installments) => {
    dispatch({ type: 'SET_INSTALLMENTS', payload: installments })
  }

  const setInvoiceInfo = (invoiceInfo) => {
    dispatch({ type: 'SET_INVOICE_INFO', payload: invoiceInfo })
  }

  const setSeatObjects = (seatObjects) => {
    dispatch({ type: 'SET_SEAT_OBJECTS', payload: seatObjects })
  }

  const setCreditInfo = (info) => {
    dispatch({ type: 'SET_CREDIT_INFO', payload: info })
  }

  const setDeliverEmail = (email) => {
    dispatch({ type: 'SET_DELIVER_EMAIL', payload: email })
  }

  const setPayInfo = (payInfo) => {
    dispatch({ type: 'SET_PAY_INFO', payload: payInfo })
  }

  const setServerCart = (cart) => {
    dispatch({ type: 'SET_SERVER_CART', payload: cart })
  }

  const setDiscountCode = (code) => {
    dispatch({ type: 'SET_DISCOUNT_CODE', payload: code })
  }

  const setPayMethod = (method) => {
    dispatch({ type: 'SET_PAY_METHOD', payload: method })
  }

  const setDonations = (donations) => {
    dispatch({ type: 'SET_DONATIONS', payload: donations })
  }

  const setStep = (step) => {
    checkStepTimer(step)
    dispatch({
      type: 'SET_PREVIOUS_STEP_LIST',
      payload: [...state.previousStepList, state.step],
    })
    absoluteSetStep(step)
  }

  const absoluteSetStep = (step) => {
    dispatch({ type: 'SET_STEP', payload: step })
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    })

    setSaveState(true)
  }

  const setEvent = (event) => {
    dispatch({ type: 'SET_EVENT', payload: event })
  }

  const setUser = (user) => {
    dispatch({ type: 'SET_USER', payload: user })
  }

  const setPurchase = (purchase) => {
    dispatch({ type: 'SET_PURCHASE', payload: purchase })
  }

  const addDonationToCart = (id, amount) => {
    const newDonation = {
      id,
      amount,
    }
    const cart = [...state.donationCart, newDonation]

    dispatch({ type: 'SET_DONATION_CART', payload: cart })
  }

  const removeDonation = (id) => {
    const cart = [...state.donationCart].filter(
      ({ id: cartID }) => cartID !== id
    )

    dispatch({ type: 'SET_DONATION_CART', payload: cart })
  }

  const editDonation = (id, amount) => {
    const cart = [...state.donationCart]
    var index = cart.findIndex(({ id: cartID }) => cartID === id)

    if (index < 0) return

    cart[index].amount = amount

    dispatch({ type: 'SET_DONATION_CART', payload: cart })
  }

  const setToPreviousStep = () => {
    const lastEventStepList = [
      STEP.Result,
      STEP.PixResult,
      STEP.TimeoutResult,
      STEP.BankSlipResult,
    ]
    const previousStepCount = state.previousStepList.length
    const returnToEventPage =
      previousStepCount === 0 ||
      lastEventStepList.findIndex((s) => s === state.step) >= 0

    if (returnToEventPage) {
      // let path = isPDV ? myEventsPath() : eventPath(slug)
      // if (token) path += `?t=${token}`
      // window.location.href = path

      if (isPDV) {
        window.location.href = myEventsPath()
      } else {
        goBackToEventPage()
      }

      return
    }
    const previousStep = state.previousStepList[previousStepCount - 1]
    checkStepTimer(previousStep)
    dispatch({
      type: 'SET_PREVIOUS_STEP_LIST',
      payload: state.previousStepList.slice(0, -1),
    })
    absoluteSetStep(previousStep)
  }

  const backToStep = (step) => {
    const index = state.previousStepList.findIndex((s) => s === step)

    if (index < 0) return

    checkStepTimer(step)
    dispatch({
      type: 'SET_PREVIOUS_STEP_LIST',
      payload: state.previousStepList.slice(0, index),
    })
    absoluteSetStep(step)
  }

  const checkStepTimer = (step) => {
    const needToStopTimer = stopTimerStepList.findIndex((s) => s === step) >= 0
    if (needToStopTimer) stopTimer()
  }

  const onTimerUpdate = useCallback((timer) => {
    if (timer < 0) {
      absoluteSetStep(STEP.TimeoutResult)
      stopTimer()
      return
    }

    printTimer(timer)
  }, [])

  const {
    startCountdown,
    stopCountdown,
    getCountdownTime,
    getTimerObject,
    restartWithTimerObject,
  } = useTimerCountDown({ onTimerUpdate })

  const printTimer = () => {
    const timer = getCountdownTime()

    const elementList = document.getElementsByName('checkout-countdown')

    let minutes = 0
    let seconds = 0

    if (!Number.isNaN(timer)) {
      minutes = Math.floor(timer / 60)
      seconds = timer % 60
    }

    const text = `${zeroPad(minutes, 2)}:${zeroPad(seconds, 2)}`

    elementList.forEach((e) => {
      e.innerText = text
    })
  }

  const startTimer = (totalTime) => {
    startCountdown(totalTime)
    printTimer()
  }

  const stopTimer = () => {
    stopCountdown()
  }

  const setTickets = (tickets) => {
    dispatch({ type: 'SET_TICKETS', payload: tickets })
  }

  const setTicketSelectedQuantity = (ticket, quantity) => {
    const id = ticket.id
    const type = ticket.type

    if (type === 'TICKET') ticketAvailabilityRecalculation(id, quantity)
    if (type === 'PACKAGE') packageAvailabilityRecalculation(id, quantity)
    updateCartSummary(ticket, quantity)
  }

  const updateCartSummary = (ticket, quantity) => {
    const type = ticket.type
    const id = ticket.id
    const name = ticket.name
    const form = ticket.form

    let list = state.cartSummary[type]
    if (!list) list = []

    const index = list.findIndex((obj) => obj.id === id)

    if (index >= 0) {
      if (quantity === 0) {
        list.splice(index, 1)
      } else {
        const obj = list[index]
        obj.quantity = quantity
      }
    } else {
      const obj = { id, quantity, type, batchId: ticket.batchId, name, form }
      list.push(obj)
    }

    const newSummary = {
      [type]: list,
    }

    dispatch({ type: 'UPDATE_CART', payload: newSummary })
  }

  const updateSeatsCartSummary = (selectedOffers) => {
    if (!selectedOffers) {
      dispatch({ type: 'UPDATE_CART', payload: {} })
      return
    }

    const { ticketBatches } = selectedOffers

    const summary = {
      ['TICKET']: Object.entries(ticketBatches).map(
        ([batchId, { seatIds, quantity, offer }]) => ({
          batchId,
          quantity,
          seatIds,
          id: offer.id,
        })
      ),
    }

    dispatch({ type: 'UPDATE_CART', payload: summary })
  }

  const packageAvailabilityRecalculation = async (id, newSelectedQuantity) => {
    const localTicketsList = []

    const selectedPackage = state.tickets?.find((ticket) => ticket.id === id)
    if (!selectedPackage) return

    localTicketsList.push({ ...selectedPackage, selected: newSelectedQuantity })

    // update max selectable quantities of tickets from packages
    const packages = state?.tickets?.filter(
      (ticket) => ticket.type === 'PACKAGE'
    )
    selectedPackage?.packageTicketOffers?.map((packageOffer) => {
      const selectedTicket = state.tickets?.find(
        (ticket) => ticket.name === packageOffer?.ticket_name
      )
      if (!selectedTicket) return

      const allTicketSelections = allPackageTicketSelectionsByTicketName(
        mergeLocalTicketsWithStateTickets(localTicketsList, state.tickets),
        packageOffer.ticket_name
      )

      let updatedMaxSelectableQuantity =
        selectedTicket?.originalRemainingAmount - allTicketSelections

      const updatedTicket = {
        ...selectedTicket,
        maxSelectableQuantity: updatedMaxSelectableQuantity,
      }

      if (
        !localTicketsList.find((ticket) => ticket.name === updatedTicket.name)
      ) {
        localTicketsList.push(updatedTicket)
      } else {
        const index = localTicketsList.findIndex(
          (ticket) => ticket.name === updatedTicket.name
        )
        localTicketsList[index] = updatedTicket
      }
    })

    const mergedTicketList = mergeLocalTicketsWithStateTickets(
      localTicketsList,
      state.tickets
    )

    // update all packages max selectable quantities with updated tickets max selectable quantities
    packages.map((ticketPackage) => {
      let updatedMaxSelectableQuantity =
        ticketPackage.name === selectedPackage.name
          ? selectedPackage.maxSelectableQuantity
          : calculateMaxPackages(ticketPackage, mergedTicketList)

      updatedMaxSelectableQuantity = Math.min(
        updatedMaxSelectableQuantity,
        ticketPackage.maxQuantityAvailableForSale,
        ticketPackage.purchaseableQuantities
      )

      const localTicket = localTicketsList.find(
        (ticket) => ticket.name === ticketPackage.name
      )
      if (localTicket) {
        localTicket.maxSelectableQuantity = updatedMaxSelectableQuantity
      } else {
        const updatedPackage = {
          ...ticketPackage,
          maxSelectableQuantity: updatedMaxSelectableQuantity,
        }
        localTicketsList.push(updatedPackage)
      }
    })

    dispatch({
      type: 'UPDATE_ALL_TICKETS',
      payload: mergeLocalTicketsWithStateTickets(
        localTicketsList,
        state.tickets
      ),
    })
  }

  const ticketAvailabilityRecalculation = (id, newSelectedQuantity) => {
    const localTicketsList = []

    const selectedTicket = state.tickets?.find((ticket) => ticket.id === id)
    const updatedTicket = { ...selectedTicket, selected: newSelectedQuantity }

    localTicketsList.push(updatedTicket)

    const packagesWithSelectedTicket = state.tickets?.filter((packageOffer) =>
      Boolean(
        packageOffer?.packageTicketOffers?.find(
          (ticket) => ticket?.ticket_name === selectedTicket?.name
        )
      )
    )

    if (!packagesWithSelectedTicket.length) return

    // update all packages with selected ticket
    packagesWithSelectedTicket.map((packageOffer) => {
      const mergedTicketList = mergeLocalTicketsWithStateTickets(
        localTicketsList,
        state.tickets
      )

      const updatedMaxSelectableQuantity = calculateMaxPackages(
        packageOffer,
        mergedTicketList
      )

      const localTicket = localTicketsList.find(
        (ticket) => ticket.name === packageOffer?.name
      )
      if (localTicket) {
        localTicket.maxSelectableQuantity = updatedMaxSelectableQuantity
      } else {
        const updatedPackage = {
          ...packageOffer,
          maxSelectableQuantity: updatedMaxSelectableQuantity,
        }
        localTicketsList.push(updatedPackage)
      }
    })

    dispatch({
      type: 'UPDATE_ALL_TICKETS',
      payload: mergeLocalTicketsWithStateTickets(
        localTicketsList,
        state.tickets
      ),
    })
  }

  useEffect(() => {
    if (saveState) {
      if (!isPDV) saveStateToStorage()
      setSaveState(false)
    }
  }, [saveState, isPDV])

  const saveStateToStorage = () => {
    const saveState = { ...state, timerObj: getTimerObject() }
    sessionStorage.setItem(checkoutStorageKey, JSON.stringify(saveState))
  }

  const tryLoadStateFromStorage = () => {
    const statetxt = sessionStorage.getItem(checkoutStorageKey)

    if (!statetxt) return false

    const parsedState = JSON.parse(statetxt)

    if (!parsedState) return false

    const { timerObj, ...state } = parsedState

    if (slug !== state.event.slug) return false

    const success = restartWithTimerObject(timerObj)

    if (!success) return false

    setState(state)
    setloadingEventOffer(false)

    return true
  }

  useEffect(() => {
    const success = !isPDV && tryLoadStateFromStorage()
    if (!success) {
      fetchEventOffer()
    }
  }, [])

  const setState = (state) => {
    dispatch({ type: 'SET_STATE', payload: state })
  }

  const goBackToEventPage = () => {
    let path = eventPath(state.event.slug)

    const pathParams = {}

    if (token) pathParams['t'] = token
    if (trackingLinkID) pathParams['tracking_list'] = trackingLinkID

    var paramList = Object.entries(pathParams)

    for (let i = 0; i < paramList.length; i++) {
      const prefix = i == 0 ? '?' : '&'
      const [key, value] = paramList[i]
      path += `${prefix}${key}=${value}`
    }

    window.open(path, '_self')
  }

  return (
    <CheckoutContext.Provider
      value={{
        state,
        dispatch,
        setStep,
        setToPreviousStep,
        startTimer,
        stopTimer,
        setTickets,
        setTicketSelectedQuantity,
        updateSeatsCartSummary,
        setPurchase,
        setUser,
        setDeliverEmail,
        setPayInfo,
        setCreditInfo,
        printTimer,
        backToStep,
        loading: loadingEventOffer,
        setServerCart,
        setDiscountCode,
        setPayMethod,
        fetchEventOffer,
        setCategories,
        setSeatObjects,
        setInvoiceInfo,
        updateServerCart,
        setInstallments,
        setParticipantData,
        setHoldToken,
        token,
        trackEvent,
        analyticsInstance,
        isPurchasePaid,
        isPDV,
        trackingLinkID,
        goBackToEventPage,
        addDonationToCart,
        removeDonation,
        editDonation,
        pushToDataLayer
      }}
    >
      {children}
    </CheckoutContext.Provider>
  )
})

export { CheckoutContext, CheckoutProvider }
