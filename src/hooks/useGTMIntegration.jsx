import { pushGTMEvent } from 'config/gtm'
import { useEffect } from 'react'

export const useGTMIntegration = (integrations) => {
  useEffect(() => {
    if (!integrations || !window) return

    const gtmID = integrations.find((v) => v.name === 'google_ads')?.value

    if (!gtmID) return undefined
    ;(function (w, d, s, l, i) {
      w[l] = w[l] || []
      w[l].push({ 'gtm.blocklist': ['html'] })
      w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
      var f = d.getElementsByTagName(s)[0],
        j = d.createElement(s),
        dl = l != 'dataLayer' ? '&l=' + l : ''
      j.async = true
      j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl
      f.parentNode.insertBefore(j, f)
    })(window, document, 'script', 'dataLayer', gtmID)
  }, [integrations])

  const pushToDataLayer = (gtmEventName) => {
    window?.dataLayer?.push({
      event: gtmEventName,
    })
  }

  return { pushToDataLayer }
}
