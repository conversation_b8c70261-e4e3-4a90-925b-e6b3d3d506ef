import createReducer from 'src/utils/reducer';

const checkoutHandlers = {
  SET_STEP: (state, action) => ({ ...state, step: action.payload }),
  SET_PREVIOUS_STEP_LIST: (state, action) => ({ ...state, previousStepList: action.payload }),
  SET_FORM_DATA: (state, action) => ({ ...state, formData: action.payload }),
  SET_PAYMENT_DATA: (state, action) => ({ ...state, paymentData: action.payload }),
  SET_COUPON: (state, action) => ({ ...state, coupon: action.payload }),
  SET_TICKETS: (state, action) => ({ ...state, tickets: action.payload }),
  SET_DONATIONS: (state, action) => ({ ...state, donations: action.payload }),
  SET_DONATION_CART: (state, action) => ({ ...state, donationCart: action.payload }),
  SET_USER: (state, action) => ({ ...state, user: action.payload }),
  SET_EVENT: (state, action) => ({ ...state, event: action.payload }),
  SET_PURCHASE: (state, action) => ({ ...state, purchase: action.payload }),
  SET_DELIVER_EMAIL: (state, action) => ({ ...state, deliverToMail: action.payload }),
  SET_PAY_INFO: (state, action) => ({ ...state, payInfo: action.payload }),
  SET_CREDIT_INFO: (state, action) => ({ ...state, creditInfo: action.payload }),
  SET_SERVER_CART: (state, action) => ({ ...state, serverCartSummary: action.payload }),
  SET_DISCOUNT_CODE: (state, action) => ({ ...state, discountCode: action.payload }),
  SET_PAY_METHOD: (state, action) => ({ ...state, payMethod: action.payload }),
  SET_CATEGORIES: (state, action) => ({ ...state, categories: action.payload }),
  SET_SEAT_OBJECTS: (state, action) => ({ ...state, seatObjects: action.payload }),
  SET_INVOICE_INFO: (state, action) => ({ ...state, invoiceInfo: action.payload }),
  SET_INSTALLMENTS: (state, action) => ({ ...state, installments: action.payload }),
  SET_HOLD_TOKEN: (state, action) => ({ ...state, holdToken: action.payload }),
  SET_PARTICIPANT_DATA: (state, action) => ({ ...state, participantData: action.payload }),
  SET_STATE: (state, action) => ({ ...action.payload }),
  UPDATE_TICKET: (state, action) => ({ ...state, tickets: state.tickets.map((ticket) => ticket.id === action.payload.id ? action.payload : ticket) }),
  UPDATE_ALL_TICKETS: (state, action) => ({ ...state, tickets: action.payload }),
  UPDATE_CART: (state, action) =>({...state, cartSummary: {
    ...state.cartSummary,
    ...action.payload
  }})
};

const checkoutReducer = createReducer(checkoutHandlers);

export default checkoutReducer;
