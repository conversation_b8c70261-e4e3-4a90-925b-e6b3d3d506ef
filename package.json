{"name": "lets-webapp", "version": "0.0.2", "scripts": {"start": "cross-env NODE_ENV=production NODE_OPTIONS=--openssl-legacy-provider node server.js -p 8080", "apolloFragments": "node config/createPossibleTypes.js", "build:next": "NODE_OPTIONS=--openssl-legacy-provider next build", "dev": "cross-env NODE_ENV=development npm run apolloFragments && cross-env NODE_ENV=development NODE_OPTIONS=--openssl-legacy-provider node server.js", "debug": "cross-env NODE_ENV=development node --inspect server.js", "dev:lint:js": "eslint --ignore-path .gitignore .", "dev:lint:fix": "eslint --ignore-path .gitignore . --fix", "heroku-postbuild": "node -v && npm run apolloFragments && npm run build:next && find build/static -type f -name '*.map' -delete", "devbuild": "cross-env DEV_BUILD=true npm run heroku-postbuild", "devstart": "cross-env DEV_BUILD=true npm start", "reinstall": "rm -rf ./node_modules ./build && npm install", "size:check": "cross-env BUNDLE_ANALYZE=browser npm run build:next", "gcp-build": "npm run heroku-postbuild", "deploy-prod": "gcloud config set project moonlit-album-200218 && gcloud app deploy app-prod.yaml --version=0-0-5", "create-env": "printenv > .env && printenv > ./config/.env"}, "repository": {"type": "git", "url": "https://github.com/letsevents/webapp"}, "author": "Lets.events <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/letsevents/webapp/issues"}, "homepage": "https://github.com/letsevents/webapp#readme", "dependencies": {"@apollo/client": "^3.5.9", "@blueprintjs/colors": "^4.1.1", "@blueprintjs/core": "^4.2.1", "@blueprintjs/datetime": "4.0.3", "@blueprintjs/icons": "4.2.0", "@blueprintjs/popover2": "^2.1.18", "@blueprintjs/select": "4.0.3", "@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-brands-svg-icons": "^6.3.0", "@fortawesome/free-regular-svg-icons": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@fortawesome/react-fontawesome": "^0.2.0", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.1.4", "@fullcalendar/react": "^6.0.4", "@google-pay/save-button-react": "^1.0.0", "@lets-events/react": "^10.0.1", "@lets-events/tokens": "^6.0.0", "@next/bundle-analyzer": "^12.1.4", "@react-pdf/renderer": "^3.3.8", "@stripe/react-stripe-js": "1.16.5", "@stripe/stripe-js": "1.48.0", "@tiptap/extension-text-align": "^2.2.6", "@tiptap/extension-underline": "^2.2.6", "@tiptap/extension-youtube": "^2.3.0", "@tiptap/pm": "^2.2.6", "@tiptap/react": "^2.2.6", "@tiptap/starter-kit": "^2.2.6", "accept-language-parser": "^1.5.0", "acorn": "^8.7.1", "apexcharts": "^3.33.0", "apollo-link-error": "^1.1.13", "axios": "^1.6.2", "babel-polyfill": "^6.26.0", "body-parser": "^1.20.2", "body-scroll-lock": "^3.1.3", "calendar-link": "^2.8.0", "card-validator": "^8.1.1", "classnames": "^2.3.1", "compression": "^1.7.4", "compressorjs": "^1.1.1", "cookie": "^0.4.0", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "css-minimizer-webpack-plugin": "^3.4.1", "dayjs": "^1.11.7", "dexie": "^3.2.0", "element-closest": "^3.0.2", "elt-react-credit-cards": "0.0.1", "express": "^4.17.2", "fbemitter": "^3.0.0", "fbjs": "^3.0.2", "final-form": "^4.20.6", "final-form-arrays": "^3.0.2", "fullcalendar": "^5.0.0-beta.2", "google-auth-library": "^5.9.2", "graphql": "^15.3.0", "isomorphic-fetch": "^3.0.0", "jsonwebtoken": "^8.5.1", "lets-i18n": "^0.2.5", "load-script": "^2.0.0", "lodash": "^4.17.21", "lru-cache": "^7.3.1", "mailcheck": "^1.1.1", "md5-dir": "^0.3.0", "mini-css-extract-plugin": "^2.6.0", "moment": "^2.29.1", "moment-timezone": "^0.5.35", "newrelic": "^8.7.1", "next": "^11.1.4", "next-compose-plugins": "^2.2.1", "normalize.css": "^8.0.1", "nprogress": "^1.0.0-1", "papaparse": "^5.3.1", "plyr": "^3.7.2", "prop-types": "^15.8.1", "querystring": "^0.2.1", "quill-auto-links": "^0.1.3", "quill-blot-formatter": "^1.0.5", "qz-tray": "^2.2.4", "react": "^17.0.2", "react-apexcharts": "^1.3.9", "react-burger-menu": "^3.0.6", "react-calendar": "^4.2.1", "react-copy-to-clipboard": "^5.0.4", "react-cropper": "^2.1.8", "react-day-picker": "^7.4.10", "react-dnd": "^15.1.2", "react-dnd-html5-backend": "^15.1.2", "react-dom": "^17.0.2", "react-dom-confetti": "^0.2.0", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.6", "react-final-form": "^6.5.7", "react-final-form-arrays": "^3.1.3", "react-ga4": "^2.1.0", "react-google-recaptcha": "^2.1.0", "react-grid-system": "^8.0.1", "react-input-mask": "^2.0.4", "react-onclickoutside": "^6.12.1", "react-otp-input": "^3.1.1", "react-phone-input-2": "^2.15.0", "react-places-autocomplete": "^7.3.0", "react-player": "^2.9.0", "react-qrcode-logo": "^2.5.0", "react-quill": "^2.0.0", "react-router-dom": "^5.1.2", "react-s3-uploader": "^5.0.0", "react-sidebar": "^3.0.2", "react-step-wizard": "^5.3.11", "react-tooltip": "^5.28.0", "react-transition-group": "^4.4.2", "rollbar": "^2.7.1", "serialize-javascript": "^6.0.0", "setprototypeof": "^1.2.0", "terser": "^5.10.0", "ua-parser-js": "^1.0.2", "uuid": "^8.3.2", "uuidv4": "^6.2.13"}, "devDependencies": {"@lets_events/rollbar-sourcemap-webpack-plugin": "^2.5.0", "autoprefixer": "^10.4.2", "babel-eslint": "^10.1.0", "babel-jest": "^27.5.1", "babel-plugin-lodash": "^3.3.4", "babel-plugin-module-resolver": "^5.0.0", "css-loader": "^6.7.1", "dotenv": "^14.2.0", "eslint": "^8.7.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "extract-css-chunks-webpack-plugin": "^4.9.0", "glob": "^7.2.0", "lodash-webpack-plugin": "^0.11.6", "prettier": "^2.5.1", "resolve-url-loader": "^5.0.0", "sass": "^1.51.0", "sass-loader": "^12.6.0", "style-loader": "^3.3.1", "webpack": "^5.72.1"}, "jest": {"setupFiles": ["<rootDir>/config/dotenv.js"], "coveragePathIgnorePatterns": ["<rootDir>/config"]}, "engines": {"node": "20.19.1"}, "prettier": {"trailingComma": "es5", "tabWidth": 2, "semi": false, "singleQuote": true}}