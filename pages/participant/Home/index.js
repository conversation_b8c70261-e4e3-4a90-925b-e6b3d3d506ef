import React, { Fragment } from 'react'
import withAppRoot from 'src/components/WithAppRoot'
import { HomeOpenGraph } from 'src/utils/openGraph'
import HomePage from 'src/pages/participant/HomePage'
import Head from 'next/head'
import Script from 'next/script'
import { getGtmTrackingCode, GTM_TRACKING_ID } from 'config/gtm'

export default withAppRoot(
  class extends React.Component {
    static translations() {
      return {
        home: 'home/home',
        eventList: 'home/eventList',
        participantFooter: 'participantFooter',
        newhome: 'home/newHome',
      }
    }

    render() {
      return (
        <Fragment>
          <Head>
            <script
              src="https://apis.google.com/js/platform.js"
              type="text/javascript"
            ></script>
            <script
              async
              src="https://www.googletagmanager.com/gtag/js?id=G-MWEXRYHE74"
            ></script>
            <Script>
              {
                'window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag("js", new Date());gtag("config", "G-MWEXRYHE74");'
              }
            </Script>

            <Script>
              {
                "(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\"https://www.googletagmanager.com/gtm.js?id=\"+i+dl;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','GTM-5PJF8LK');"
              }
            </Script>

            <script
              dangerouslySetInnerHTML={{
                __html: getGtmTrackingCode(this.props.appConfig),
              }}
            />
          </Head>
          <noscript>
            <iframe
              src={`https://www.googletagmanager.com/ns.html?id=${GTM_TRACKING_ID}`}
              height="0"
              width="0"
              style={{ display: 'none', visibility: 'hidden' }}
            />
          </noscript>
          <HomeOpenGraph />
          <HomePage />
        </Fragment>
      )
    }
  },
  true
)
