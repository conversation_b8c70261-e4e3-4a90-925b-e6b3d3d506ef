import React from 'react'
import withAppRoot from 'src/components/WithAppRoot'
import ConfigurationPage from 'src/pages/admin/Event/Configuration/ConfigurationPage'

class ConfigurationsNextPage extends React.Component {
  static translations = () => ({
    sidemenu: 'eventManager/sidemenu',
    eventStatus: 'eventManager/eventStatus',
    widgetPage: 'eventManager/widgetPage',
    configurationPage: 'eventManager/configurationPage',
    publishDialog: 'eventManager/publishDialog'
  })

  render() {
    return <ConfigurationPage />
  }
}

export default withAppRoot(ConfigurationsNextPage)
