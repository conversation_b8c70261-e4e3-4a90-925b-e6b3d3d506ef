import React from 'react'
import withAppRoot from 'src/components/WithAppRoot'
import AdmConfigPage from 'src/pages/admin/AdmConfigPage'

class ConfigurationsNextPage extends React.Component {
  static translations = () => ({
    sidemenu: 'eventManager/sidemenu',
    eventStatus: 'eventManager/eventStatus',
    widgetPage: 'eventManager/widgetPage',
    configurationPage: 'eventManager/configurationPage',
    publishDialog: 'eventManager/publishDialog',
    admin: 'admin/sidebar',
    wallet: 'admin/wallet',
    invoiceInfo: 'myBankAccounts/invoiceInfo',
  })

  render() {
    return <AdmConfigPage />
  }
}

export default withAppRoot(ConfigurationsNextPage)
