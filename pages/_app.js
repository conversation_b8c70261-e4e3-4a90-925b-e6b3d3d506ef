import React from 'react'
import App from 'next/app'
import Router from 'next/router'
import NProgress from 'nprogress'
import { parse } from 'url'
import { rollbar } from 'config/rollbar'
import { importCss } from 'config/loadCssChunksManifest'
import { CookieConsent } from 'src/hooks/useCookieConsent'

import 'src/styles/scss/vendor-styles'
import 'src/styles/scss/main.scss'

export const HrefContext = React.createContext()
class AppRoot extends App {
  // Only uncomment this method if you have blocking data requirements for
  // every single page in your application. This disables the ability to
  // perform automatic static optimization, causing every page in your app to
  // be server-side rendered.
  //
  // static async getInitialProps(appContext) {
  //   // calls page's `getInitialProps` and fills `appProps.pageProps`
  //   const appProps = await App.getInitialProps(appContext)

  //   return { ...appProps }
  // }
  hrefRef = React.createRef()

  componentDidMount() {
    NProgress.configure({ showSpinner: false })
    this.triggerPageview(Router.asPath)

    Router.events.on('routeChangeStart', () => {
      if (this.hrefRef.current) {
        const { pathname } = parse(this.hrefRef.current)
        importCss(pathname).catch((e) => {
          rollbar.error(e)
        })
      }
      NProgress.start()
    })

    Router.events.on('routeChangeComplete', (url) => {
      // TODO: This setTimeout is being used as a work around to a nextjs bug that
      // causes the document.title to not be updated by the time routeChangeComplete
      // is fired. It can be removed once the issue
      // https://github.com/zeit/next.js/issues/6025 is resolved
      setTimeout(() => {
        this.triggerPageview(url)
      })
      NProgress.done()
    })

    Router.events.on('routeChangeError', () => {
      NProgress.done()
    })

    // Register the service worker from the public directory
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker
        .register('/service-worker.js')
        .then((registration) => {
          console.log(
            'Service Worker registered with scope:',
            registration.scope
          )
        })
        .catch((registrationError) => {
          console.log('Service Worker registration failed:', registrationError)
        })
    }
  }

  triggerPageview = (url) => {
    window?.dataLayer?.push({
      event: 'pageview',
      page: {
        path: url,
        title: document.title,
      },
    })
  }

  setHref = (href) => {
    this.hrefRef.current = href
  }

  render() {
    const { Component, pageProps } = this.props
    return (
      <HrefContext.Provider value={this.setHref}>
        <CookieConsent />
        <Component {...pageProps} />
      </HrefContext.Provider>
    )
  }
}

export default AppRoot
