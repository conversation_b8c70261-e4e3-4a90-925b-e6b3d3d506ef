/* eslint-disable */
/*
 * https://github.com/zeit/next.js#custom-document
 */

import Document, { Html, Head, Main, NextScript } from 'next/document'
import { getAppConfig } from 'config/appConfig'
import serialize from 'serialize-javascript'
import { rollbar } from 'config/rollbar'
import { loadCssManifest } from 'config/loadCssChunksManifest'
import { isDev } from 'src/utils'

export default class extends Document {
  static async getInitialProps(ctx) {
    const { renderPage, req } = ctx
    const [initialProps, appConfig = {}] = await Promise.all(
      [
        Document.getInitialProps(ctx),
        getAppConfig({ cookies: req?.cookies }),
      ].map((promise) =>
        promise.catch((error) => {
          rollbar.error(error)
          if (isDev()) {
            throw error
          }
        })
      )
    )

    return { ...initialProps, appConfig, cssManifest: loadCssManifest() }
  }

  render() {
    return (
      <Html lang="pt-BR">
        <Head>
          {process.env.NODE_ENV === 'development' && (
            <style
              dangerouslySetInnerHTML={{
                __html: 'pre { background-color: transparent !important }',
              }}
            />
          )}

          <script
            dangerouslySetInnerHTML={{
              __html: `window.__CSS_CHUNKS__ = JSON.parse('${JSON.stringify(
                this.props.cssManifest
              )}')`,
            }}
          />

          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link
            rel="preconnect"
            href="https://fonts.gstatic.com"
            crossOrigin="true"
          />
          <link
            href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@100;400;500;600&display=swap"
            rel="stylesheet"
          />

          <link
            rel="shortcut icon"
            type="image/x-icon"
            href="https://s3-sa-east-1.amazonaws.com/lets.events-production/assets/lets-favicon.png"
          />

          <script
            dangerouslySetInnerHTML={{
              __html: `window.appConfig = ${serialize(this.props.appConfig)}`,
            }}
          />
          <link rel="preconnect" href="https://fonts.googleapis.com" />
          <link
            rel="preconnect"
            href="https://fonts.gstatic.com"
            crossOrigin="true"
          />
          <link
            href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@300;400;500;600&display=swap"
            rel="stylesheet"
          ></link>
          <link
            href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600&display=swap"
            rel="stylesheet"
          ></link>
        </Head>
        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}
