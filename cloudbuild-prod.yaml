steps:
  - name: node:20.19.1
    entrypoint: npm
    args: ["run", "create-env"]
    env:
      - 'APP_DOMAIN=${_APP_DOMAIN}'
      - 'AWS_ACCESS_KEY_ID=${_AWS_ACCESS_KEY_ID}'
      - 'AWS_BUCKET=${_AWS_BUCKET}'
      - 'AWS_REGION=${_AWS_REGION}'
      - 'AWS_SECRET_ACCESS_KEY=${_AWS_SECRET_ACCESS_KEY}'
      - 'AWS_URL=${_AWS_URL}'
      - 'COOKIE_PREFIX=${_COOKIE_PREFIX}'
      - 'DOMAIN_NAME=${_DOMAIN_NAME}'
      - 'FB_APP_ID=${_FB_APP_ID}'
      - 'GOOGLE_APP_ID=${_GOOGLE_APP_ID}'
      - 'GOOGLE_MAPS_API_KEY=${_GOOGLE_MAPS_API_KEY}'
      - 'GOOGLE_OAUTH_REDIRECT_URI=${_GOOGLE_OAUTH_REDIRECT_URI}'
      - 'GTM_TRACKING_ID=${_GTM_TRACKING_ID}'
      - 'KONDUTO_PUBLIC_KEY=${_KONDUTO_PUBLIC_KEY}'
      - 'LEGACY_APP_ROOT=${_LEGACY_APP_ROOT}'
      - 'LETS_API=${_LETS_API}'
      - 'LETS_APP_NAME=${_LETS_APP_NAME}'
      - 'MAINTENANCE_PAGE_URL=${_MAINTENANCE_PAGE_URL}'
      - 'NEW_RELIC_HOME=${_NEW_RELIC_HOME}'
      - 'NEW_RELIC_LICENSE_KEY=${_NEW_RELIC_LICENSE_KEY}'
      - 'OAUTH_REDIRECT_URI=${_OAUTH_REDIRECT_URI}'
      - 'PAPERTRAIL_API_TOKEN=${_PAPERTRAIL_API_TOKEN}'
      - 'RECAPTCHA_KEY=${_RECAPTCHA_KEY}'
      - 'ROLLBAR_ACCESS_TOKEN_CLIENT=${_ROLLBAR_ACCESS_TOKEN_CLIENT}'
      - 'ROLLBAR_ACCESS_TOKEN_SERVER=${_ROLLBAR_ACCESS_TOKEN_SERVER}'
      - 'ROLLBAR_ACCESS_TOKEN=${_ROLLBAR_ACCESS_TOKEN}'
      - 'ROLLBAR_ENDPOINT=${_ROLLBAR_ENDPOINT}'
      - 'ROOT_PATH=${_ROOT_PATH}'
      - 'ROOT_URL=${_ROOT_URL}'
      - 'SEATS_IO_PUBLIC_KEY=${_SEATS_IO_PUBLIC_KEY}'
      - 'SEATS_IO_SECRET_KEY=${_SEATS_IO_SECRET_KEY}'
      - 'SUPPORT_PATH=${_SUPPORT_PATH}'
      - 'WEBAPP_SEO_DOMAIN=${_WEBAPP_SEO_DOMAIN}'
      - 'ROLLBAR_ENDPOINT=${_ROLLBAR_ENDPOINT}'
      - 'USE_HTTPS=${_USE_HTTPS}'
      - 'FB_PIXEL_ACCESS_TOKEN=${_FB_PIXEL_ACCESS_TOKEN}'
      - 'TAG_NAME=$TAG_NAME'
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk
    args:
      - '-c'
      - >-
        tag=$TAG_NAME &&
        version=${tag//./-} &&
        printenv && gcloud config set app/cloud_build_timeout 1600 && gcloud config set
        project moonlit-album-200218 && gcloud app deploy app-prod.yaml
        --version=${version}
    entrypoint: bash
    env:
      - 'APP_DOMAIN=${_APP_DOMAIN}'
      - 'AWS_ACCESS_KEY_ID=${_AWS_ACCESS_KEY_ID}'
      - 'AWS_BUCKET=${_AWS_BUCKET}'
      - 'AWS_REGION=${_AWS_REGION}'
      - 'AWS_SECRET_ACCESS_KEY=${_AWS_SECRET_ACCESS_KEY}'
      - 'AWS_URL=${_AWS_URL}'
      - 'COOKIE_PREFIX=${_COOKIE_PREFIX}'
      - 'DOMAIN_NAME=${_DOMAIN_NAME}'
      - 'FB_APP_ID=${_FB_APP_ID}'
      - 'GOOGLE_APP_ID=${_GOOGLE_APP_ID}'
      - 'GOOGLE_MAPS_API_KEY=${_GOOGLE_MAPS_API_KEY}'
      - 'GOOGLE_OAUTH_REDIRECT_URI=${_GOOGLE_OAUTH_REDIRECT_URI}'
      - 'GTM_TRACKING_ID=${_GTM_TRACKING_ID}'
      - 'KONDUTO_PUBLIC_KEY=${_KONDUTO_PUBLIC_KEY}'
      - 'LEGACY_APP_ROOT=${_LEGACY_APP_ROOT}'
      - 'LETS_API=${_LETS_API}'
      - 'LETS_APP_NAME=${_LETS_APP_NAME}'
      - 'MAINTENANCE_PAGE_URL=${_MAINTENANCE_PAGE_URL}'
      - 'NEW_RELIC_HOME=${_NEW_RELIC_HOME}'
      - 'NEW_RELIC_LICENSE_KEY=${_NEW_RELIC_LICENSE_KEY}'
      - 'OAUTH_REDIRECT_URI=${_OAUTH_REDIRECT_URI}'
      - 'PAPERTRAIL_API_TOKEN=${_PAPERTRAIL_API_TOKEN}'
      - 'RECAPTCHA_KEY=${_RECAPTCHA_KEY}'
      - 'ROLLBAR_ACCESS_TOKEN_CLIENT=${_ROLLBAR_ACCESS_TOKEN_CLIENT}'
      - 'ROLLBAR_ACCESS_TOKEN_SERVER=${_ROLLBAR_ACCESS_TOKEN_SERVER}'
      - 'ROLLBAR_ACCESS_TOKEN=${_ROLLBAR_ACCESS_TOKEN}'
      - 'ROLLBAR_ENDPOINT=${_ROLLBAR_ENDPOINT}'
      - 'ROOT_PATH=${_ROOT_PATH}'
      - 'ROOT_URL=${_ROOT_URL}'
      - 'SEATS_IO_PUBLIC_KEY=${_SEATS_IO_PUBLIC_KEY}'
      - 'SEATS_IO_SECRET_KEY=${_SEATS_IO_SECRET_KEY}'
      - 'SUPPORT_PATH=${_SUPPORT_PATH}'
      - 'WEBAPP_SEO_DOMAIN=${_WEBAPP_SEO_DOMAIN}'
      - 'ROLLBAR_ENDPOINT=${_ROLLBAR_ENDPOINT}'
      - 'USE_HTTPS=${_USE_HTTPS}'
      - 'FB_PIXEL_ACCESS_TOKEN=${_FB_PIXEL_ACCESS_TOKEN}'
      - 'TAG_NAME=$TAG_NAME'
timeout: 1600s
