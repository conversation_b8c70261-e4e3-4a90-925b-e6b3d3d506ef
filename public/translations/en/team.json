{"eventTeamTitle": "Event Team", "organizationTeamTitle": "Organization Team", "organizationTeamDescription": "List of members added to the organization's team.", "inviteButton": "Add member", "searchPlaceholder": "Search by name", "ownerSectionTitle": "Owner", "warningMessage": "<b>Warning!</b> People you add to the organization team will have access to all events in your account. To restrict access to specific events, add the person directly in the 'Event Team' tab.", "warningMessageLink": "Go to Event Team!", "permissions": {"OWNER": "Owner", "DOORMEN": "Check-in", "SALES_ANALYSTS": "Sales Report", "MASTER_ADMINS": "Administrator", "PDV_OPERATOR": "PDV Operator"}, "teamMember": {"name": "Collaborator name", "email": "Email", "permissions": "Permissions", "team": {"title": "Team", "organization": "Organization", "event": "Event"}, "status": {"label": "Status", "accepted": "Accepted", "pending": "Pending"}, "actions": {"editPermissions": "Edit Permissions", "delete": "Delete"}, "resendTooltip": "Resend invite to team member.", "ownerTooltip": "Team owners cannot be removed or have their permissions changed.", "globalTeam": "This collaborator is part of the organization's team, to edit permissions or remove them you must access the organization's team page. <a href='%LINK%'>Go to organization's team</a>"}, "emptyState": {"title": "No collaborators added", "description": "It seems your team is empty. Add collaborators so they can contribute to your event!", "button": "Add Collaborator"}, "createConfirmModal": {"title": "Attention!", "subtitle": "People you add to my team will have access to all events in your account.", "description": "If you prefer the person to collaborate on specific events, go to the event and add them to the event team tab.", "confirm": "Send Invite", "cancel": "Back"}, "deleteConfirmModal": {"title": "Attention!", "description": "By removing this collaborator from your team, they will no longer be able to collaborate on your event. Are you sure you want to remove them?", "confirm": "Yes, Delete", "cancel": "Cancel", "successMessage": "Collaborator successfully removed"}, "memberDrawer": {"inviteTitle": "Invite Collaborator", "editTitle": "Edit Permissions", "name": "Full Name", "namePlaceholder": "Collaborator's Full Name", "email": "Collaborator's <PERSON><PERSON>", "mandatoryField": "Mandatory Field!", "permissions": {"title": "Permissions", "description": "Define what your collaborator will be able to do", "MASTER_ADMINS": "Administrator", "DOORMEN": "Guest Check-in", "SALES_ANALYSTS": "View Sales Reports", "PDV_OPERATOR": "PDV Operator"}, "continue": "Continue", "save": "Save Changes", "cancel": "Cancel", "addSuccessMemberMessage": "Collaborator successfully added", "editSuccessMemberMessage": "Collaborator successfully edited"}, "resendInviteModal": {"title": "Resend Invite", "label": "Resend invite to collaborator", "send": "Send", "successMessage": "Invitation successfully resent"}}