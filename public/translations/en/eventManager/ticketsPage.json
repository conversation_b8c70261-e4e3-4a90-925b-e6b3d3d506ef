{"createTicket": "Create ticket", "forms": "Forms", "interestFreePayment": "Interest-free installments", "discountCodes": "Discount codes", "editTicketButton": "Edit purchase button", "eventCapacityButton": "Capacidade do evento", "customizeExibition": "Customize exibition", "reorderTickets": "Rearrange tickets", "updateTicketMessage": "Custom ticket message", "batchDisplay": "Configure batches display", "exportAll": "Export all", "printAll": "Print all", "participants": "Participants", "shareDialog": {"title": "Purchase Link", "linkText": "Purchase Link", "message": "Anybody with this link can purchase tickets."}, "nonIdealStates": {"error": {"title": "Oops! Seems like something went wrong.", "description": "Sorry! It seems like something went wrong while we were retrieving tickets data. Please try again.", "actionText": "Try again"}, "searchResultIsEmpty": {"title": "No ticket found", "description": "It seems like the ticket you are looking for does not exist or is not part of this event."}, "empty": {"title": "You haven't created tickets yet", "actionText": "Create ticket"}}, "newDialog": {"titleDesktop": "Create Ticket", "titleMobile": "Choose the type of ticket", "description": "Which ticket type would you like to create?", "simple": {"name": "Simple Ticket", "description": "The most frequent choice. Just define a price and a quantity and you'll be ready to start selling.", "button": "Create simple ticket"}, "batch": {"name": "Batched Ticket", "description": "Define how and when ticket batches are rolled out. Increase your average ticket price and encourage early purchasing.", "button": "Create ticket in batches"}, "free": {"name": "Free Ticket", "description": "The name says it all, it's really free :)", "button": "Create free ticket"}, "donation": {"name": "Donation", "description": "Allow your guests to donate to your cause", "button": "Create donation"}}, "dataGrid": {"public": "Public", "private": "Private", "purchaseLink": "Purchase Link", "singlePrice": "Single Price", "allBatches": "See batches (%NUM)", "hideBatches": "<PERSON>de", "quantity": "Quantity", "participants": "Participants", "duplicate": "Duplicate", "edit": "Edit", "export": "Export", "print": "Print", "remove": "Remove", "endsIn": "Ends in: %NUM days", "closedAt": "Closed on %DATE", "singleValue": "Single value", "batchValue": "Batch value", "atValue": "at", "batch": "batch #%NUM", "soldOut": "Sold out", "ticketsAvailable": "Remaining: %NUM tickets", "forms": "Participation form", "publish": "Resume sales", "unpublish": "Pause sales", "unpublished": "Sales paused", "progressBar": {"soldTickets": "Confirmed", "pendingTickets": "Pending"}}, "removeTicket": {"body": "Are you sure you want to remove this ticket?", "cancel": "Cancel", "confirm": "Yes, remove", "expire": "Remove ticket", "loading": "Removing ticket...", "succeed": "Removed", "failed": "Sorry, something went wrong and we could not remove this ticket."}, "exportTicket": {"loading": "Exporting this ticket...", "succeed": "Exported", "failed": "Sorry, something went wrong and we could not export this ticket."}, "duplicateTicket": {"loading": "Duplicating this ticket...", "succeed": "Duplicated", "failed": "Sorry, we couldn't duplicate this ticket."}, "filterBar": {"placeholderInput": "Search for name..."}, "loadMore": "Load more", "publishTicket": {"loading": "Resuming Sales...", "succeed": "The sales have been resumed", "failed": "Sorry, something went wrong and we could not resume the sales"}, "unpublishTicket": {"loading": "Pausing sales...", "succeed": "The sales have been paused", "failed": "Sorry, something went wrong and we could not pause the sales"}, "removeDisabled": "This ticket cannot be removed because it has ongoing purchases. You may pause the sales to avoid new purchases.", "ticketReorder": {"title": "Reorder public tickets", "callout": "Use the arrows to define the ordering of public tickets on the checkout screen.", "update": {"loading": "Saving changes", "success": "Changes saved successfully", "failed": "Oops, there was an error saving your changes. Please try again or contact our support if the error persists."}, "error": {"title": "Oops! Something went wrong.", "description": "Oops! It looks like something went wrong while trying to reorder tickets. Please try again.", "actionText": "Try again"}}, "configBatchDisplay": {"title": "Configure batches and tickets display", "subtite": "Which batches and tickets do you want to display on the sales screen?", "all": "All of them, including past and future", "available": "Only those currently available for sale", "remember": "Make this a default setting for other events", "update": {"loading": "Saving changes", "success": "Changes saved successfully", "failed": "Failed to save changes"}, "error": {"title": "Oops! Something went wrong.", "description": "An error occurred while trying to load this page. Please try again.", "actionText": "Try again"}}, "configTicketCustomMessage": {"title": "Ticket custom message", "subtitle": "The message will be added to each purchased ticket file that is sent to the buyer via email and via download link under My Tickets menu", "message": "Message"}, "drawer": {"title": "Set up interest-free installments", "subtitle": "Choose the value of the Lets service fee for payments on the card and offer interest-free installments.", "label_title": {"custom": "Personalized rate", "default": "Standard rate", "tax_of": "Rate of"}, "installment": "Installment", "interest_free": "interest-free", "with_interest": "with interest", "up_to": "up to", "observation": "Note: the chosen service fee will be charged regardless of the number of installments the buyer chooses. The minimum amount of the installment is always R$5.00.", "callout": {"custom": "Your event has a personalized service fee.", "default": "Offer interest-free installments to your customers and increase your sales. You receive the money at once at the closing of the event.", "no_options": "There are currently no interest-free installment options for your event.", "unavailable": "The plan in which your event was set up is no longer available. If you change it, you will not be able to return to the current plan"}}, "configEventCapacity": {"title": "Capacidade máxima do evento", "text": "Este é o número máximo de ingressos que podem ser vendidos no seu evento, independente da capacidade de cada tipo de ingresso.", "inputLabel": "Capacidade total do evento", "cta": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "configTicketPackage": {"title": "Criar combo de ingressos", "inputLabel": "Nome do combo de ingressos", "inputPlaceholder": "Combo de ingressos sem título", "priceLabel": "Valor do pacote", "pricePlaceholder": "R$0,00", "cta": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "ticketWrapper": {"title": {"public": "Public Ticket", "private": "Private Ticket"}, "confirmation": {"title": "Attention!", "text": {"toPrivate": "Are you sure you want to change your public ticket to a private one?", "toPublic": "Are you sure you want to change your private ticket to a public one?"}, "cta": "Yes, I want to change", "cancel": "Cancel"}}, "publicTicketButton": {"text": "<b>Attention!</b> You have not created any public tickets yet, so the event page will not have a ticket purchase button.", "knowMore": "Learn more"}, "createDropdownOptions": {"ticket": "Create ticket", "category": "Create category", "package": "Create ticket package"}, "category": {"drawer": {"title": "category", "edit": "Edit", "create": "Create", "createLoading": "Creating category...", "createSuccess": "Category created successfully.", "updateLoading": "Updating category...", "updateSuccess": "Category updated successfully."}, "form": {"name": "Category name", "namePlaceholder": "Untitled category", "nameRequired": "Category name is required.", "description": "Category description", "photo": "Category image", "photoMaxSize": "Maximum size", "photoDimension": "Dimension", "photoFormat": "Format", "save": "Save", "cancel": "Cancel"}, "title": "Categories", "infoCard": {"name": "Category name", "totalTickets": "Total tickets applied", "totalRevenue": "Total revenue", "singularTicket": "ticket", "pluralTicket": "tickets", "edit": "Edit", "remove": "Delete", "removeLoading": "Deleting category...", "removeSuccess": "Category deleted successfully."}}}