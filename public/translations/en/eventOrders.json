{"donationTag": "Donation", "donationValue": "Total", "paymentState": {"IN_PROCESS": "Pending", "IN_PROCESS__AWAITING_REVIEW": "In review", "IN_PROCESS__IN_MEDIATION": "Payment in mediation", "IN_PROCESS__PENDING_BANK_SLIP": "Pending", "INACTIVE": "Inactive", "INACTIVE__REFUNDED": "Refunded", "INACTIVE__CANCELED__AND__REFUNDED": "Canceled and refunded", "INACTIVE__REVIEW_DECLINED": "Payment declined", "INACTIVE__SLIP_EXPIRED": "Bank Slip Expired", "INACTIVE__CANCELED": "Canceled", "PAID": "Approved", "PAID__REFUNDED_TOO_LATE": "Approved"}, "paymentType": {"BANK_SLIP": "Bank Slip", "COMPLIMENTARY_TICKETS": "Complimentary Tickets", "CREDIT": "Card %NUMx", "CREDIT_INTERNATIONAL": "International credit card", "FREE": "Free", "LETS_DEPOSIT": "Bank Deposit (via Lets)", "MANUAL": "Manual", "PIX": "Pix", "OFFLINE_BANK_SLIP": "Bank Slip (offline)", "OFFLINE_CASH": "Cash (offline)", "OFFLINE_CREDIT": "Credit (offline)", "OFFLINE_DEBIT": "Debit (offline)", "OFFLINE_DEPOSIT": "Deposit (offline)", "OFFLINE": "Manual purchase", "WALLET": "Me<PERSON><PERSON>", "PDV": "PDV"}, "actions": {"downloadBankSlip": "Download bank slip", "ticketDownload": "Download all tickets", "viewDetails": "View details", "cancelPurchase": "Cancel purchase", "cancelBankslip": "Cancel bank slip", "cancelAndRefund": "Cancel and refund"}, "buyer": "Buyer", "buyerNotProvided": "Not provided", "createdBy": "Created by", "viewAll": "See all", "hide": "<PERSON>de", "participants": "Participants", "purchaseDate": "Purchase date", "orderNum": "Order #", "noResultsDescription": "Could not find any order.", "noResultsTitle": "Nothing to show here...", "filterBar": {"empty": "All orders", "placeholder": "Search for name, email...", "label": "Show:"}, "export": "Export (.xls)", "moreOrders": "Load more", "drawerOrder": {"ticketsOrder": "Tickets summary", "order": "Order %NUM", "participantInfo": "Participant info", "ticketNumber": "Ticket #", "totalFee": "Service fee", "totalPurchase": "Total purchase", "close": "Close", "participant": {"address": "Address", "cnpj": "CNPJ", "cpf": "CPF", "identity_document_number": "RG", "name": "Name", "phone_number": "Phone", "birthday": "Birthday", "gender": "Gender", "email": "Email", "company": "Company", "job_title": "Job title", "website": "Website", "linkedin": "LinkedIn", "whatsapp": "WhatsApp"}}, "nonIdealStates": {"error": {"title": "It seems like something went wrong", "description": "Sorry! It seems like something went wrong while we were retrieving orders data. Please try again.", "actionText": "Try again"}, "searchResultIsEmpty": {"title": "No order found with the selected filter."}, "empty": {"title": "No sales have yet been made.", "actionText": "Create order"}}, "ticketDownload": {"loading": "Downloading ticket...", "success": "Ticket successfully downloaded", "failed": "Sorry, something went wrong and we could not download this ticket"}, "exportOrders": {"loading": "Exporting orders...", "success": "Orders exported successfully", "failed": "Sorry, something went wrong and we could not export orders."}, "filterDialog": {"title": "What orders would you like to export?", "all": "All Orders", "filtered": "Orders with the selected filters:", "state": "State", "search": "Keywords", "confirm": "Export", "cancel": "Cancel"}, "cancelPurchase": {"title": "Confirm purchase cancellation", "message": "Are you sure you want to cancel this purchase?", "confirmText": "Yes, cancel purchase", "cancelText": "No thanks", "loading": "Canceling purchase...", "success": "Purchase successfully canceled", "failed": "Sorry, something went wrong and we could not cancel this purchase."}, "cancelBankslip": {"title": "Bank slip cancellation - Attention", "message1": "You should only cancel this purchase if the buyer is aware of the cancellation;", "message2": "It is possible the bank slip has already been paid but has not yet been compensated by the bank", "confirmMessages": "Are you sure the buyer hasn't paid the bank slip yet?", "confirmText": "Yes, cancel bank slip", "cancelText": "No thanks", "loading": "Canceling bank slip...", "success": "Boleto successfully canceled ", "failed": "Sorry, something went wrong and we could not cancel this bank slip."}, "cancelAndRefund": {"title": "Order cancellation - Attention", "message1": "This order's refund will show up on the statement of the credit card used by the client.", "message2": "Please let your client know that the refund will show up in the next statement or after the next statement.", "confirmMessages": "Are you sure you want to cancel and refund this order?", "confirmText": "Yes, cancel and refund this order", "cancelText": "No thanks", "loading": "Canceling and refunding order...", "success": "Order successfully canceled and refunded", "failed": "Sorry, something went wrong and we could not cancel and refund this order."}}